<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record model="ir.module.category" id="module_pr_approval_for_opex">
        <field name="name">Approval Groups for PR Opex</field>
        <field name="description">User access level for approval opex PR </field>
        <field name="sequence">12</field>
    </record>

    <record model="ir.module.category" id="module_pr_approval_for_capex_revex_service">
        <field name="name">Approval Groups for PR Capex/Revex/Service</field>
        <field name="description">User access level for approval Capex or Others PR </field>
        <field name="sequence">12</field>
    </record>

    <record id="allow_user_send_approval_for_opex" model="res.groups">
        <field name="name">User</field>
        <field name="category_id" ref="module_pr_approval_for_opex"/>
    </record>

    <record id="allow_department01_approval_for_opex" model="res.groups">
        <field name="name">Department 01</field>
        <field name="category_id" ref="module_pr_approval_for_opex"/>
    </record>
    
    <record id="allow_department02_approval_for_opex" model="res.groups">
        <field name="name">Department 02</field>
        <field name="category_id" ref="module_pr_approval_for_opex"/>
    </record>

    <record id="allow_management_approval_for_opex" model="res.groups">
        <field name="name">Management</field>
        <field name="category_id" ref="module_pr_approval_for_opex"/>
    </record>

    <record id="allow_user_send_approval_for_capex_others" model="res.groups">
        <field name="name">User</field>
        <field name="category_id" ref="module_pr_approval_for_capex_revex_service"/>
    </record>

    <record id="allow_dept_head_approval_for_capex_others" model="res.groups">
        <field name="name">Department Head</field>
        <field name="category_id" ref="module_pr_approval_for_capex_revex_service"/>
    </record>
    
    <record id="allow_ce_approval_for_capex_others" model="res.groups">
        <field name="name">CE</field>
        <field name="category_id" ref="module_pr_approval_for_capex_revex_service"/>
    </record>

</odoo>