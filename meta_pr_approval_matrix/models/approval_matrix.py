
from odoo import _, api, fields, models
from odoo.exceptions import UserError, ValidationError
import logging
_logger = logging.getLogger(__name__)




class ApprovalMatrix(models.Model):
    _name = "approval.matrix.pr"
    _description = "Approval Matrix"

    pr_initiator = fields.Many2one('res.users', string="Initiator", required=False)
    pr_initiator2 = fields.Many2many('res.users', 'res_partner_initiator_rel', 'user_initiator_id', 'user_initiator_name', string="User Initiator")
    pr_category = fields.Many2one('product.category', string="Category", required=True, domain=[("parent_id", "=", False)])
    name = fields.Char(string='Name', related='pr_category.name')
    type_of_expenditure = fields.Selection([
        ('capex', 'CapEx'),
        ('opex', 'OpEx'),
        ('revex', 'RevEx'), ], string="Type of Expenditure", required=True)

    dept_approver01 = fields.Many2one('res.users', string="Department Approver 01")
    dept_approver01_ids = fields.Many2many('res.users', 'dept_approver_o1_ids_rel', 'dept_01_user_id', 'dept_01_user_name', string="Department Approver 01")
    dept_approver_limit01 = fields.Float('Approver Limit')

    dept_approver02 = fields.Many2one('res.users', string="Department Approver 02")
    dept_approver02_ids = fields.Many2many('res.users', 'dept_approver_o2_ids_rel', 'dept_02_user_id', 'dept_02_user_name', string="Department Approver 02")
    dept_approver_limit02 = fields.Float('Approver Limit')

    mgt_approver01 = fields.Many2one('res.users', string="Mgt Approver")
    mgt_approver01_ids = fields.Many2many('res.users', 'mgt_approver_o1_ids_rel', 'mgt_01_user_id', 'mgt_01_user_name', string="Mgt Approver")
    mgt_approver_limit01 = fields.Float('Approver Limit')
    
    mgt_approver02 = fields.Many2one('res.users', string="Mgt Approver 02")   
    mgt_approver02_ids = fields.Many2many('res.users', 'mgt_approver_o2_ids_rel', 'mgt_02_user_id', 'mgt_02_user_name', string="Mgt Approver 02")
    mgt_approver_limit02 = fields.Float('Approver Limit')

    # test_initiator = fields.Many2many('res.partner', 'res_partner_initiator_rel', 'user_initiator_id', 'user_initiator_name', string="User Initiator")


    cap_rev_dept_head_approver_ids = fields.Many2many('res.users', 'cap_rev_dept_head_approver_rel', 'cap_rev_dept_head_user_id', 'cap_rev_dept_head_user_name', string="Department Head")
    cap_rev_ce_approver_ids = fields.Many2many('res.users', 'cap_rev_ce_approver_rel', 'cap_rev_ce_user_id', 'cap_rev_ce_user_name', string="CE Approver")

    # @api.model
    # def create(self, vals):
    #     res = super().create(vals)
    #     data = self.env['approval.matrix.pr'].sudo().search([])

    #     creation = False
    #     for item in data:
    #         if item.pr_initiator2.ids in res.pr_initiator2.ids and item.pr_category

        # _logger.info(f"Initiator are ------------------>{res.pr_initiator2.ids}")
        # if any(data.filtered(lambda x: res.pr_initiator2.ids in x.pr_initiator2.ids and res.pr_category == x.pr_category and res.type_of_expenditure == x.type_of_expenditure)):
        #     raise ValidationError(
        #             f"Already exists same initiator and category in the approval matrix"
        #         )
        # else:
        #     return res
    @api.model
    def create(self, vals):
        initiator1_ids = []
        initiator2_ids = []
        approval_matrix = self.env['approval.matrix.pr'].sudo().search([('type_of_expenditure', '=', vals["type_of_expenditure"]), ('pr_category', '=', vals["pr_category"])])
        for user in approval_matrix.pr_initiator2:
            initiator1_ids.append(user.id)

        for new_user in vals.get("pr_initiator2", []):
            initiator2_ids.append(new_user[1])
        
        if any(item in initiator1_ids for item in initiator2_ids):
            raise ValidationError("A record with the same users and category! in the approval matrix.")

        return super().create(vals)

    def write(self, vals):
        res = super().write(vals)
        if 'pr_initiator2' in vals or 'type_of_expenditure' in vals or 'pr_category' in vals:
            initiator1_ids = []
            initiator2_ids = []
            
            # Get type_of_expenditure and pr_category from vals or existing record
            type_of_expenditure = vals.get('type_of_expenditure', self.type_of_expenditure)
            pr_category = vals.get('pr_category', self.pr_category.id)
            
            # Search existing records excluding current record
            approval_matrix = self.env['approval.matrix.pr'].sudo().search([
                ('id', '!=', self.id),
                ('type_of_expenditure', '=', type_of_expenditure),
                ('pr_category', '=', pr_category)
            ])
            
            for user in approval_matrix.pr_initiator2:
                initiator1_ids.append(user.id)

            _logger.info(f"intiotator are --------------->>>> {vals.get('pr_initiator2', [])}")

            if 'pr_initiator2' in vals:
                if isinstance(vals['pr_initiator2'], list) and vals['pr_initiator2']:
                    # Handle command format [(6, 0, [user_ids])]
                    if vals['pr_initiator2'][0][0] == 6:
                        initiator2_ids = vals['pr_initiator2'][0][2]
                    else:
                        initiator2_ids = self.pr_initiator2.ids
                else:
                    initiator2_ids = self.pr_initiator2.ids
            else:
                initiator2_ids = self.pr_initiator2.ids
            
            if any(item in initiator1_ids for item in initiator2_ids):
                raise ValidationError("A record with the same users and category! in the approval matrix.")

        return res
    # def _check_duplicate_users(self):
    #     for record in self:
    #         # Get all existing records with the same set of users
    #         existing_records = self.env['approval.matrix.pr'].search([
    #             ('id', '!=', record.id),  # Exclude current record
    #         ])

    #         for existing in existing_records.filtered(lambda x: record.pr_initiator2.ids in x.pr_initiator2.ids):
    #             if existing.pr_category == record.pr_category and existing.type_of_expenditure == record.type_of_expenditure:  # Compare user sets
    #                 raise ValidationError("A record with the same users and category! in the approval matrix.")

    def initiator_send_approval(self, user_id=False, pr_amount=0.00):
        for rec in self:
            users = user_id
            req_amount = pr_amount
            state = ''
            status = False
            responsible_users = []
            if users in rec.pr_initiator2.ids:
                if rec.dept_approver01_ids:
                    status = True
                    state = 'department_approver01'
                    responsible_users = [(6, 0, rec.dept_approver01_ids.ids)]
                elif not rec.dept_approver01_ids and rec.dept_approver02_ids:
                    status = True
                    state = 'department_approver02'
                    responsible_users = [(6, 0, rec.dept_approver02_ids.ids)]
                else:
                    status = True
                    state = 'mgt_approver01'
                    responsible_users = [(6, 0, rec.mgt_approver01_ids.ids)]
                    
                return {
                    'status': status,
                    'state': state,
                    'responsible_users': responsible_users
                }
            else:
                raise ValidationError(_('You are not allowed to send this PR'))

    def action_department_approver01(self, user_id=False, pr_amount=0.00):
        for rec in self:
            users = user_id
            req_amount = pr_amount
            state = ''
            status = False
            responsible_users = []
            if users in rec.dept_approver01_ids.ids:
                if req_amount <= rec.dept_approver_limit01:
                    status = True
                    state = 'approved'
                    responsible_users = [(6, 0, rec.dept_approver01_ids.ids)]
                elif req_amount > rec.dept_approver_limit01 and rec.dept_approver02_ids:
                    status = True
                    state = 'department_approver02'
                    responsible_users = [(6, 0, rec.dept_approver02_ids.ids)]
                else:
                    status = True
                    state = 'mgt_approver01'
                    responsible_users = [(6, 0, rec.mgt_approver01_ids.ids)]
                return {
                    'status': status,
                    'state': state,
                    'responsible_users': responsible_users
                }
            else:
                raise ValidationError(_('You are not allowed to approve this PR'))

    def action_department_approver02(self, user_id=False, pr_amount=0.00):
        for rec in self:
            users = user_id
            req_amount = pr_amount
            state = ''
            status = False
            responsible_users = []
            if users in rec.dept_approver02_ids.ids:
                if req_amount <= rec.dept_approver_limit02:
                    status = True
                    state = 'approved'
                    responsible_users = [(6, 0, rec.dept_approver02_ids.ids)]
                else:
                    status = True
                    state = 'mgt_approver01'
                    responsible_users = [(6, 0, rec.mgt_approver01_ids.ids)]
                return {
                    'status': status,
                    'state': state,
                    'responsible_users': responsible_users
                }
            else:
                raise ValidationError(_('You are not allowed to approve this PR'))
    
    def action_mgt_approver01(self, user_id=False):
        for rec in self:
            users = user_id
            state = ''
            status = False
            if users in rec.mgt_approver01_ids.ids:
                status = True
                state = 'approved'
                return {
                    'status': status,
                    'state': state
                }
            else:
                raise ValidationError(_('You are not allowed to approve this PR'))

    def action_mgt_approver02(self, user_id=False):
        for rec in self:
            users = user_id
            state = ''
            status = False
            if users in rec.mgt_approver01_ids.ids:
                status = True
                state = 'approved'
                return {
                    'status': status,
                    'state': state
                }
            else:
                raise ValidationError(_('You are not allowed to approve this PR'))