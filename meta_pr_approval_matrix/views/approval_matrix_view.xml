<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="approval_matrix_pr_views_form" model="ir.ui.view">
        <field name="name">approval.matrix.pr.views.form</field>
        <field name="model">approval.matrix.pr</field>
        <field name="arch" type="xml">
            <form string="Approval Matrix">
                <sheet>
                    <group>
                        <group>
                            <!-- <field name="pr_initiator"  options="{'no_create': True}"/> -->
                            <field name="pr_initiator2" widget="many2many_tags"/>
                            <field name="name" invisible="1"/>
                            <field name="pr_category"  options="{'no_create': True}"/>
                            <field name="type_of_expenditure"/>
                        </group>
                    </group>
                    <group invisible="not type_of_expenditure or type_of_expenditure != 'opex'">
                        <group name="department01" string="Department 01">
                            <!-- <field name="dept_approver01"  options="{'no_create': True}"/> -->
                            <field name="dept_approver01_ids" widget="many2many_tags" options="{'no_create': True}"/>
                            <field name="dept_approver_limit01" />
                        </group>
                        <group name="department02" string="Department 02">
                            <!-- <field name="dept_approver02"  options="{'no_create': True}"/> -->
                            <field name="dept_approver02_ids" widget="many2many_tags" options="{'no_create': True}"/>
                            <field name="dept_approver_limit02" />
                        </group>
                    </group>

                    <group invisible="not type_of_expenditure or type_of_expenditure != 'opex'">
                        <group name="mgt01" string="Management">
                            <!-- <field name="mgt_approver01"  options="{'no_create': True}"/> -->
                            <field name="mgt_approver01_ids" widget="many2many_tags" options="{'no_create': True}"/>
                            <field name="mgt_approver_limit01" invisible="1"/>
                        </group>
                        <!-- <group name="mgt02" string="Management 02">
                            <field name="mgt_approver02_ids" widget="many2many_tags" options="{'no_create': True}"/>
                            <field name="mgt_approver_limit02" invisible="1"/>
                        </group> -->
                    </group>
                    <group invisible="not type_of_expenditure or type_of_expenditure == 'opex'" name="capex_revex_approval" string="Approval Matrix Capex/Revex">
                        <group>
                            <field name="cap_rev_dept_head_approver_ids"  widget="many2many_tags" options="{'no_create': True}"/>
                        </group>
                        <group>
                            <field name="cap_rev_ce_approver_ids"  widget="many2many_tags" options="{'no_create': True}"/>
                        </group>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <record id="approval_matrix_pr_views_tree" model="ir.ui.view">
        <field name="name">approval.matrix.pr.views.tree</field>
        <field name="model">approval.matrix.pr</field>
        <field name="arch" type="xml">
            <list string="Approval Matrix">
                <field name="pr_initiator2" widget="many2many_tags"/>
                <field name="pr_category"/>
                <field name="type_of_expenditure"/>
            </list>
        </field>
    </record>

    <record model="ir.actions.act_window" id="approval_matrix_pr_action">
        <field name="name">Approval Matrix</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">approval.matrix.pr</field>
        <field name="view_mode">list,form</field>
        <field name="context">{'group_by': ['type_of_expenditure', 'pr_category']}</field>
    </record>

    <menuitem
        id="menu_approval_matrix_pr_root"
        name="Approval Matrix PR"
        sequence="50"
        parent="base.menu_users"
        action="approval_matrix_pr_action"/>
</odoo>