# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * auditlog
#
# Translators:
# <AUTHOR> <EMAIL>, 2016
# <PERSON> <<EMAIL>>, 2016
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 9.0c\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-11-26 01:45+0000\n"
"PO-Revision-Date: 2024-04-17 14:35+0000\n"
"Last-Translator: mymage <<EMAIL>>\n"
"Language-Team: Italian (https://www.transifex.com/oca/teams/23907/it/)\n"
"Language: it\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: Weblate 4.17\n"

#. module: auditlog
#: model:ir.model.fields,field_description:auditlog.field_auditlog_rule__action_id
msgid "Action"
msgstr "Azione"

#. module: auditlog
#: model:ir.ui.menu,name:auditlog.menu_audit
msgid "Audit"
msgstr "Audit"

#. module: auditlog
#: model:ir.model,name:auditlog.model_auditlog_autovacuum
msgid "Auditlog - Delete old logs"
msgstr "Log autid - elimina vecchi log"

#. module: auditlog
#: model:ir.model,name:auditlog.model_auditlog_http_session
msgid "Auditlog - HTTP User session log"
msgstr "Log audit - log sessione utente HTTP"

#. module: auditlog
#: model:ir.model,name:auditlog.model_auditlog_http_request
msgid "Auditlog - HTTP request log"
msgstr "Log audit - log richiesta HTTP"

#. module: auditlog
#: model:ir.model,name:auditlog.model_auditlog_log
msgid "Auditlog - Log"
msgstr "Log audit - log"

#. module: auditlog
#: model:ir.model,name:auditlog.model_auditlog_log_line
#: model:ir.model,name:auditlog.model_auditlog_log_line_view
msgid "Auditlog - Log details (fields updated)"
msgstr "Log audit - dettagli log (campi aggiornati)"

#. module: auditlog
#: model:ir.model,name:auditlog.model_auditlog_rule
msgid "Auditlog - Rule"
msgstr "Log audit - regola"

#. module: auditlog
#: model:res.groups,name:auditlog.group_auditlog_manager
msgid "Auditlog Manager"
msgstr "Responsabile log audit"

#. module: auditlog
#: model:ir.module.category,name:auditlog.security_auditlog_groups
msgid "Auditlog Rights"
msgstr "Autorizzazioni log audit"

#. module: auditlog
#: model:res.groups,name:auditlog.group_auditlog_user
msgid "Auditlog User"
msgstr "Utente log audit"

#. module: auditlog
#: model:ir.actions.server,name:auditlog.ir_cron_auditlog_autovacuum_ir_actions_server
msgid "Auto-vacuum audit logs"
msgstr "Svuota automaticamente log audit"

#. module: auditlog
#: model:ir.model.fields,field_description:auditlog.field_auditlog_rule__capture_record
msgid "Capture Record"
msgstr "Registra record"

#. module: auditlog
#: model:ir.model.fields,field_description:auditlog.field_auditlog_http_request__user_context
msgid "Context"
msgstr "Contesto"

#. module: auditlog
#: model:ir.model.fields,field_description:auditlog.field_auditlog_autovacuum__create_uid
#: model:ir.model.fields,field_description:auditlog.field_auditlog_http_request__create_uid
#: model:ir.model.fields,field_description:auditlog.field_auditlog_http_session__create_uid
#: model:ir.model.fields,field_description:auditlog.field_auditlog_log__create_uid
#: model:ir.model.fields,field_description:auditlog.field_auditlog_log_line__create_uid
#: model:ir.model.fields,field_description:auditlog.field_auditlog_log_line_view__create_uid
#: model:ir.model.fields,field_description:auditlog.field_auditlog_rule__create_uid
msgid "Created by"
msgstr "Creato da"

#. module: auditlog
#: model:ir.model.fields,field_description:auditlog.field_auditlog_autovacuum__create_date
#: model:ir.model.fields,field_description:auditlog.field_auditlog_http_request__create_date
#: model:ir.model.fields,field_description:auditlog.field_auditlog_http_session__create_date
#: model:ir.model.fields,field_description:auditlog.field_auditlog_log__create_date
#: model:ir.model.fields,field_description:auditlog.field_auditlog_log_line__create_date
#: model:ir.model.fields,field_description:auditlog.field_auditlog_log_line_view__create_date
#: model:ir.model.fields,field_description:auditlog.field_auditlog_rule__create_date
#: model_terms:ir.ui.view,arch_db:auditlog.view_auditlog_http_request_search
#: model_terms:ir.ui.view,arch_db:auditlog.view_auditlog_http_session_search
msgid "Created on"
msgstr "Creato il"

#. module: auditlog
#: model_terms:ir.ui.view,arch_db:auditlog.view_auditlog_line_search
#: model_terms:ir.ui.view,arch_db:auditlog.view_auditlog_log_search
msgid "Date"
msgstr "Data"

#. module: auditlog
#: model:ir.model.fields,field_description:auditlog.field_auditlog_log_line__field_description
#: model:ir.model.fields,field_description:auditlog.field_auditlog_log_line_view__field_description
msgid "Description"
msgstr "Descrizione"

#. module: auditlog
#: model:ir.model.fields,field_description:auditlog.field_auditlog_autovacuum__display_name
#: model:ir.model.fields,field_description:auditlog.field_auditlog_log__display_name
#: model:ir.model.fields,field_description:auditlog.field_auditlog_log_line__display_name
#: model:ir.model.fields,field_description:auditlog.field_auditlog_log_line_view__display_name
#: model:ir.model.fields,field_description:auditlog.field_auditlog_rule__display_name
msgid "Display Name"
msgstr "Nome visualizzato"

#. module: auditlog
#: model:ir.model.fields.selection,name:auditlog.selection__auditlog_rule__state__draft
#: model_terms:ir.ui.view,arch_db:auditlog.view_auditlog_rule_search
msgid "Draft"
msgstr "Bozza"

#. module: auditlog
#: model:ir.model.fields.selection,name:auditlog.selection__auditlog_log__log_type__fast
#: model:ir.model.fields.selection,name:auditlog.selection__auditlog_rule__log_type__fast
msgid "Fast log"
msgstr "Log rapido"

#. module: auditlog
#: model:ir.model.fields,field_description:auditlog.field_auditlog_log_line__field_id
#: model:ir.model.fields,field_description:auditlog.field_auditlog_log_line_view__field_id
#: model_terms:ir.ui.view,arch_db:auditlog.view_auditlog_line_search
msgid "Field"
msgstr "Campo"

#. module: auditlog
#. odoo-python
#: code:addons/auditlog/models/rule.py:0
msgid "Field 'model_id' cannot be empty."
msgstr "Il campo \"model_id\" non può essere vuoto."

#. module: auditlog
#: model:ir.model.fields,field_description:auditlog.field_auditlog_rule__fields_to_exclude_ids
msgid "Fields to Exclude"
msgstr "Campi da escludere"

#. module: auditlog
#: model:ir.model.fields,field_description:auditlog.field_auditlog_log__line_ids
#: model_terms:ir.ui.view,arch_db:auditlog.view_auditlog_log_form
msgid "Fields updated"
msgstr "Campi aggiornati"

#. module: auditlog
#: model:ir.model.fields.selection,name:auditlog.selection__auditlog_log__log_type__full
#: model:ir.model.fields.selection,name:auditlog.selection__auditlog_rule__log_type__full
msgid "Full log"
msgstr "Log completo"

#. module: auditlog
#: model:ir.model.fields,help:auditlog.field_auditlog_rule__log_type
msgid ""
"Full log: make a diff between the data before and after the operation (log "
"more info like computed fields which were updated, but it is slower)\n"
"Fast log: only log the changes made through the create and write operations "
"(less information, but it is faster)"
msgstr ""
"Log completo: verifica differenze tra i dati prima e dopo l'operazione "
"(registra più info come campi calcolati che sono stati aggiornati, ma è più "
"lento)\n"
"Log rapido: registra solo le modifiche fatte tramite le operazioni crea e "
"scrivi (meno info, ma più veloce)"

#. module: auditlog
#: model_terms:ir.ui.view,arch_db:auditlog.view_auditlog_http_request_search
#: model_terms:ir.ui.view,arch_db:auditlog.view_auditlog_http_session_search
#: model_terms:ir.ui.view,arch_db:auditlog.view_auditlog_line_search
#: model_terms:ir.ui.view,arch_db:auditlog.view_auditlog_log_search
#: model_terms:ir.ui.view,arch_db:auditlog.view_auditlog_rule_search
msgid "Group By..."
msgstr "Raggruppa per..."

#. module: auditlog
#: model_terms:ir.ui.view,arch_db:auditlog.view_auditlog_log_form
msgid "HTTP Context"
msgstr "Contesto HTTP"

#. module: auditlog
#: model:ir.model.fields,field_description:auditlog.field_auditlog_log__http_request_id
#: model:ir.model.fields,field_description:auditlog.field_auditlog_log_line_view__http_request_id
#: model_terms:ir.ui.view,arch_db:auditlog.view_auditlog_http_request_form
#: model_terms:ir.ui.view,arch_db:auditlog.view_auditlog_line_search
#: model_terms:ir.ui.view,arch_db:auditlog.view_auditlog_log_search
msgid "HTTP Request"
msgstr "Richiesta HTTP"

#. module: auditlog
#: model:ir.actions.act_window,name:auditlog.action_auditlog_http_request_tree
#: model:ir.model.fields,field_description:auditlog.field_auditlog_http_session__http_request_ids
#: model:ir.ui.menu,name:auditlog.menu_action_auditlog_http_request_tree
#: model_terms:ir.ui.view,arch_db:auditlog.view_auditlog_http_request_search
#: model_terms:ir.ui.view,arch_db:auditlog.view_auditlog_http_session_form
msgid "HTTP Requests"
msgstr "Richieste HTTP"

#. module: auditlog
#: model:ir.model.fields,field_description:auditlog.field_auditlog_autovacuum__id
#: model:ir.model.fields,field_description:auditlog.field_auditlog_http_request__id
#: model:ir.model.fields,field_description:auditlog.field_auditlog_http_session__id
#: model:ir.model.fields,field_description:auditlog.field_auditlog_log__id
#: model:ir.model.fields,field_description:auditlog.field_auditlog_log_line__id
#: model:ir.model.fields,field_description:auditlog.field_auditlog_log_line_view__id
#: model:ir.model.fields,field_description:auditlog.field_auditlog_rule__id
msgid "ID"
msgstr "ID"

#. module: auditlog
#: model:ir.model.fields,field_description:auditlog.field_auditlog_autovacuum__write_uid
#: model:ir.model.fields,field_description:auditlog.field_auditlog_http_request__write_uid
#: model:ir.model.fields,field_description:auditlog.field_auditlog_http_session__write_uid
#: model:ir.model.fields,field_description:auditlog.field_auditlog_log__write_uid
#: model:ir.model.fields,field_description:auditlog.field_auditlog_log_line__write_uid
#: model:ir.model.fields,field_description:auditlog.field_auditlog_log_line_view__write_uid
#: model:ir.model.fields,field_description:auditlog.field_auditlog_rule__write_uid
msgid "Last Updated by"
msgstr "Ultimo aggiornamento di"

#. module: auditlog
#: model:ir.model.fields,field_description:auditlog.field_auditlog_autovacuum__write_date
#: model:ir.model.fields,field_description:auditlog.field_auditlog_http_request__write_date
#: model:ir.model.fields,field_description:auditlog.field_auditlog_http_session__write_date
#: model:ir.model.fields,field_description:auditlog.field_auditlog_log__write_date
#: model:ir.model.fields,field_description:auditlog.field_auditlog_log_line__write_date
#: model:ir.model.fields,field_description:auditlog.field_auditlog_log_line_view__write_date
#: model:ir.model.fields,field_description:auditlog.field_auditlog_rule__write_date
msgid "Last Updated on"
msgstr "Ultimo aggiornamento il"

#. module: auditlog
#: model:ir.model.fields,field_description:auditlog.field_auditlog_log_line__log_id
#: model:ir.model.fields,field_description:auditlog.field_auditlog_log_line_view__log_id
#: model_terms:ir.ui.view,arch_db:auditlog.view_auditlog_log_form
msgid "Log"
msgstr "Log"

#. module: auditlog
#: model_terms:ir.ui.view,arch_db:auditlog.view_auditlog_log_form
msgid "Log - Field updated"
msgstr "Log - campo aggiornato"

#. module: auditlog
#: model:ir.model.fields,field_description:auditlog.field_auditlog_rule__log_create
msgid "Log Creates"
msgstr "Log creazioni"

#. module: auditlog
#: model:ir.model.fields,field_description:auditlog.field_auditlog_rule__log_unlink
msgid "Log Deletes"
msgstr "Log eliminazioni"

#. module: auditlog
#: model:ir.actions.act_window,name:auditlog.action_auditlog_line
#: model:ir.ui.menu,name:auditlog.menu_auditlog_line
#: model_terms:ir.ui.view,arch_db:auditlog.view_auditlog_line_search
msgid "Log Lines"
msgstr "Log righe"

#. module: auditlog
#: model:ir.model.fields,field_description:auditlog.field_auditlog_rule__log_read
msgid "Log Reads"
msgstr "Log letture"

#. module: auditlog
#: model:ir.model.fields,field_description:auditlog.field_auditlog_rule__log_write
msgid "Log Writes"
msgstr "Log scritture"

#. module: auditlog
#: model:ir.actions.act_window,name:auditlog.action_auditlog_log_tree
#: model:ir.model.fields,field_description:auditlog.field_auditlog_http_request__log_ids
#: model:ir.ui.menu,name:auditlog.menu_audit_logs
#: model_terms:ir.ui.view,arch_db:auditlog.view_auditlog_http_request_form
#: model_terms:ir.ui.view,arch_db:auditlog.view_auditlog_log_search
msgid "Logs"
msgstr "Log"

#. module: auditlog
#: model:ir.model.fields,field_description:auditlog.field_auditlog_log__method
#: model:ir.model.fields,field_description:auditlog.field_auditlog_log_line_view__method
msgid "Method"
msgstr "Metodo"

#. module: auditlog
#: model:ir.model.fields,field_description:auditlog.field_auditlog_log__model_id
#: model:ir.model.fields,field_description:auditlog.field_auditlog_log_line_view__model_id
#: model:ir.model.fields,field_description:auditlog.field_auditlog_rule__model_id
#: model_terms:ir.ui.view,arch_db:auditlog.view_auditlog_line_search
#: model_terms:ir.ui.view,arch_db:auditlog.view_auditlog_log_search
msgid "Model"
msgstr "Modello"

#. module: auditlog
#: model:ir.model.fields,field_description:auditlog.field_auditlog_log_line_view__model_model
msgid "Model Model"
msgstr "Modello Model"

#. module: auditlog
#: model:ir.model.fields,field_description:auditlog.field_auditlog_log__model_name
#: model:ir.model.fields,field_description:auditlog.field_auditlog_log_line_view__model_name
#: model:ir.model.fields,field_description:auditlog.field_auditlog_rule__model_name
msgid "Model Name"
msgstr "Nome modello"

#. module: auditlog
#: model:ir.model.fields,field_description:auditlog.field_auditlog_http_request__display_name
#: model:ir.model.fields,field_description:auditlog.field_auditlog_http_session__display_name
#: model:ir.model.fields,field_description:auditlog.field_auditlog_log_line_view__name
#: model:ir.model.fields,field_description:auditlog.field_auditlog_rule__name
msgid "Name"
msgstr "Nome"

#. module: auditlog
#: model:ir.model.fields,field_description:auditlog.field_auditlog_log_line__new_value
#: model:ir.model.fields,field_description:auditlog.field_auditlog_log_line_view__new_value
msgid "New Value"
msgstr "Nuovo valore"

#. module: auditlog
#: model:ir.model.fields,field_description:auditlog.field_auditlog_log_line__new_value_text
#: model:ir.model.fields,field_description:auditlog.field_auditlog_log_line_view__new_value_text
msgid "New value Text"
msgstr "Testo nuovo valore"

#. module: auditlog
#. odoo-python
#: code:addons/auditlog/models/log.py:0
msgid "No field defined to create line."
msgstr "Nessun campo definito per creare una riga."

#. module: auditlog
#. odoo-python
#: code:addons/auditlog/models/rule.py:0
msgid "No model defined to create line."
msgstr "Nessun modello definito per creare una riga."

#. module: auditlog
#. odoo-python
#: code:addons/auditlog/models/log.py:0
msgid "No model defined to create log."
msgstr "Nessun modello definito per creare un log."

#. module: auditlog
#: model:ir.model.fields,field_description:auditlog.field_auditlog_log_line__old_value
#: model:ir.model.fields,field_description:auditlog.field_auditlog_log_line_view__old_value
msgid "Old Value"
msgstr "Vecchio valore"

#. module: auditlog
#: model:ir.model.fields,field_description:auditlog.field_auditlog_log_line__old_value_text
#: model:ir.model.fields,field_description:auditlog.field_auditlog_log_line_view__old_value_text
msgid "Old value Text"
msgstr "Testo vecchio valore"

#. module: auditlog
#: model:ir.model.fields,field_description:auditlog.field_auditlog_http_request__name
#: model_terms:ir.ui.view,arch_db:auditlog.view_auditlog_http_request_search
msgid "Path"
msgstr "Percorso"

#. module: auditlog
#: model:ir.model.fields,field_description:auditlog.field_auditlog_log_line_view__res_id
msgid "Res"
msgstr "Res"

#. module: auditlog
#: model:ir.model.fields,field_description:auditlog.field_auditlog_log__res_id
#: model_terms:ir.ui.view,arch_db:auditlog.view_auditlog_line_search
#: model_terms:ir.ui.view,arch_db:auditlog.view_auditlog_log_search
msgid "Resource ID"
msgstr "ID Risorsa"

#. module: auditlog
#: model:ir.model.fields,field_description:auditlog.field_auditlog_log__name
msgid "Resource Name"
msgstr "Nome risorsa"

#. module: auditlog
#: model:ir.model.fields,field_description:auditlog.field_auditlog_http_request__root_url
#: model_terms:ir.ui.view,arch_db:auditlog.view_auditlog_http_request_search
msgid "Root URL"
msgstr "URL root"

#. module: auditlog
#: model_terms:ir.ui.view,arch_db:auditlog.view_auditlog_rule_form
msgid "Rule"
msgstr "Regola"

#. module: auditlog
#: model:ir.actions.act_window,name:auditlog.action_auditlog_rule_tree
#: model:ir.ui.menu,name:auditlog.menu_action_auditlog_rule_tree
#: model_terms:ir.ui.view,arch_db:auditlog.view_auditlog_rule_search
msgid "Rules"
msgstr "Regole"

#. module: auditlog
#: model:ir.model.fields,help:auditlog.field_auditlog_rule__model_id
msgid "Select model for which you want to generate log."
msgstr "Seleziona modello per il quale si vuole generare un log."

#. module: auditlog
#: model:ir.model.fields,help:auditlog.field_auditlog_rule__capture_record
msgid "Select this if you want to keep track of Unlink Record"
msgstr "Selezionare se si vuole tenere traccia di Unlink Record"

#. module: auditlog
#: model:ir.model.fields,help:auditlog.field_auditlog_rule__log_create
msgid ""
"Select this if you want to keep track of creation on any record of the model "
"of this rule"
msgstr ""
"Selezionare se si vuole tenere traccia della creazione di qualsiasi record "
"del modello di questa regola"

#. module: auditlog
#: model:ir.model.fields,help:auditlog.field_auditlog_rule__log_unlink
msgid ""
"Select this if you want to keep track of deletion on any record of the model "
"of this rule"
msgstr ""
"Selezionare se si vuole tenere traccia dell'eliminazione di qualsiasi record "
"del modello di questa regola"

#. module: auditlog
#: model:ir.model.fields,help:auditlog.field_auditlog_rule__log_write
msgid ""
"Select this if you want to keep track of modification on any record of the "
"model of this rule"
msgstr ""
"Selezionare se si vuole tenere traccia della modifica di qualsiasi record "
"del modello di questa regola"

#. module: auditlog
#: model:ir.model.fields,help:auditlog.field_auditlog_rule__log_read
msgid ""
"Select this if you want to keep track of read/open on any record of the "
"model of this rule"
msgstr ""
"Selezionare se si vuole tenere traccia della lettura/apertura di qualsiasi "
"record del modello di questa regola"

#. module: auditlog
#: model:ir.model.fields,field_description:auditlog.field_auditlog_http_request__http_session_id
#: model:ir.model.fields,field_description:auditlog.field_auditlog_log__http_session_id
#: model:ir.model.fields,field_description:auditlog.field_auditlog_log_line_view__http_session_id
msgid "Session"
msgstr "Sessione"

#. module: auditlog
#: model:ir.model.fields,field_description:auditlog.field_auditlog_http_session__name
msgid "Session ID"
msgstr "ID Sessione"

#. module: auditlog
#: model:ir.model.fields,field_description:auditlog.field_auditlog_rule__state
#: model_terms:ir.ui.view,arch_db:auditlog.view_auditlog_rule_search
msgid "State"
msgstr "Stato"

#. module: auditlog
#: model_terms:ir.ui.view,arch_db:auditlog.view_auditlog_rule_form
msgid "Subscribe"
msgstr "Sottoscrivi"

#. module: auditlog
#: model:ir.model.fields.selection,name:auditlog.selection__auditlog_rule__state__subscribed
#: model_terms:ir.ui.view,arch_db:auditlog.view_auditlog_rule_search
msgid "Subscribed"
msgstr "Sottoscritto"

#. module: auditlog
#: model:ir.model.fields,field_description:auditlog.field_auditlog_log__model_model
#: model:ir.model.fields,field_description:auditlog.field_auditlog_rule__model_model
msgid "Technical Model Name"
msgstr "Nome tecnico modello"

#. module: auditlog
#: model:ir.model.fields,field_description:auditlog.field_auditlog_log_line__field_name
#: model:ir.model.fields,field_description:auditlog.field_auditlog_log_line_view__field_name
msgid "Technical name"
msgstr "Nome tecnico"

#. module: auditlog
#. odoo-python
#: code:addons/auditlog/models/log.py:0
msgid "The field 'field_id' cannot be empty."
msgstr "Il campo 'field_id' non può essere vuoto."

#. module: auditlog
#. odoo-python
#: code:addons/auditlog/models/log.py:0
msgid "The field 'model_id' cannot be empty."
msgstr "Il campo 'model_id' non può essere vuoto."

#. module: auditlog
#: model:ir.model.constraint,message:auditlog.constraint_auditlog_rule_model_uniq
msgid ""
"There is already a rule defined on this model\n"
"You cannot define another: please edit the existing one."
msgstr ""
"Esiste già una regola definita per questo modello\n"
"Non puoi definirne un'altra; modifica quella esistente."

#. module: auditlog
#: model:ir.model.fields,field_description:auditlog.field_auditlog_log__log_type
#: model:ir.model.fields,field_description:auditlog.field_auditlog_log_line_view__log_type
#: model:ir.model.fields,field_description:auditlog.field_auditlog_rule__log_type
msgid "Type"
msgstr "Tipo"

#. module: auditlog
#: model_terms:ir.ui.view,arch_db:auditlog.view_auditlog_rule_form
msgid "Unsubscribe"
msgstr "Disiscrivi"

#. module: auditlog
#: model:ir.model.fields,field_description:auditlog.field_auditlog_http_request__user_id
#: model:ir.model.fields,field_description:auditlog.field_auditlog_http_session__user_id
#: model:ir.model.fields,field_description:auditlog.field_auditlog_log__user_id
#: model:ir.model.fields,field_description:auditlog.field_auditlog_log_line_view__user_id
#: model_terms:ir.ui.view,arch_db:auditlog.view_auditlog_http_request_search
#: model_terms:ir.ui.view,arch_db:auditlog.view_auditlog_http_session_search
#: model_terms:ir.ui.view,arch_db:auditlog.view_auditlog_line_search
#: model_terms:ir.ui.view,arch_db:auditlog.view_auditlog_log_search
msgid "User"
msgstr "Utente"

#. module: auditlog
#: model_terms:ir.ui.view,arch_db:auditlog.view_auditlog_http_request_search
#: model_terms:ir.ui.view,arch_db:auditlog.view_auditlog_http_session_form
#: model_terms:ir.ui.view,arch_db:auditlog.view_auditlog_line_search
#: model_terms:ir.ui.view,arch_db:auditlog.view_auditlog_log_search
msgid "User session"
msgstr "Sessione utente"

#. module: auditlog
#: model:ir.actions.act_window,name:auditlog.action_auditlog_http_session_tree
#: model:ir.ui.menu,name:auditlog.menu_action_auditlog_http_session_tree
#: model_terms:ir.ui.view,arch_db:auditlog.view_auditlog_http_session_search
msgid "User sessions"
msgstr "Sessioni utente"

#. module: auditlog
#: model:ir.model.fields,field_description:auditlog.field_auditlog_rule__user_ids
msgid "Users"
msgstr "Utenti"

#. module: auditlog
#: model:ir.model.fields,field_description:auditlog.field_auditlog_rule__users_to_exclude_ids
msgid "Users to Exclude"
msgstr "Utenti da escludere"

#. module: auditlog
#: model_terms:ir.ui.view,arch_db:auditlog.view_auditlog_log_form
msgid "Values"
msgstr "Valori"

#. module: auditlog
#. odoo-python
#: code:addons/auditlog/models/rule.py:0
msgid "View logs"
msgstr "Visualizza log"

#. module: auditlog
#: model:ir.model.fields,help:auditlog.field_auditlog_rule__user_ids
msgid "if no user is added then it will applicable for all users"
msgstr "se l'utente non è aggiunto, sarà applicabile per tutti gli utenti"

#~ msgid "Last Modified on"
#~ msgstr "Ultima modifica il"
