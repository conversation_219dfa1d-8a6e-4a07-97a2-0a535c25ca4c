# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * auditlog
#
# Translators:
# <AUTHOR> <EMAIL>, 2016
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 9.0c\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-11-26 01:45+0000\n"
"PO-Revision-Date: 2024-06-12 11:45+0000\n"
"Last-Translator: j<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Swedish (https://www.transifex.com/oca/teams/23907/sv/)\n"
"Language: sv\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: Weblate 4.17\n"

#. module: auditlog
#: model:ir.model.fields,field_description:auditlog.field_auditlog_rule__action_id
msgid "Action"
msgstr "Åtgärd"

#. module: auditlog
#: model:ir.ui.menu,name:auditlog.menu_audit
msgid "Audit"
msgstr "Revision"

#. module: auditlog
#: model:ir.model,name:auditlog.model_auditlog_autovacuum
msgid "Auditlog - Delete old logs"
msgstr "Auditlog - Ta bort gamla loggar"

#. module: auditlog
#: model:ir.model,name:auditlog.model_auditlog_http_session
msgid "Auditlog - HTTP User session log"
msgstr "Auditlog - HTTP-sessionslogg för användare"

#. module: auditlog
#: model:ir.model,name:auditlog.model_auditlog_http_request
msgid "Auditlog - HTTP request log"
msgstr "Auditlog - Logg över HTTP-förfrågningar"

#. module: auditlog
#: model:ir.model,name:auditlog.model_auditlog_log
msgid "Auditlog - Log"
msgstr "Auditlog - Logg"

#. module: auditlog
#: model:ir.model,name:auditlog.model_auditlog_log_line
#: model:ir.model,name:auditlog.model_auditlog_log_line_view
msgid "Auditlog - Log details (fields updated)"
msgstr "Auditlog - Loggdetaljer (fält uppdaterade)"

#. module: auditlog
#: model:ir.model,name:auditlog.model_auditlog_rule
msgid "Auditlog - Rule"
msgstr "Auditlog - Regel"

#. module: auditlog
#: model:res.groups,name:auditlog.group_auditlog_manager
msgid "Auditlog Manager"
msgstr "Auditlog Manager"

#. module: auditlog
#: model:ir.module.category,name:auditlog.security_auditlog_groups
msgid "Auditlog Rights"
msgstr "Rättigheter för Auditlog"

#. module: auditlog
#: model:res.groups,name:auditlog.group_auditlog_user
msgid "Auditlog User"
msgstr "Auditlog Användare"

#. module: auditlog
#: model:ir.actions.server,name:auditlog.ir_cron_auditlog_autovacuum_ir_actions_server
msgid "Auto-vacuum audit logs"
msgstr "Granskningsloggar för automatisk vakuumering"

#. module: auditlog
#: model:ir.model.fields,field_description:auditlog.field_auditlog_rule__capture_record
msgid "Capture Record"
msgstr "Upptagningsrekord"

#. module: auditlog
#: model:ir.model.fields,field_description:auditlog.field_auditlog_http_request__user_context
msgid "Context"
msgstr "Sammanhang"

#. module: auditlog
#: model:ir.model.fields,field_description:auditlog.field_auditlog_autovacuum__create_uid
#: model:ir.model.fields,field_description:auditlog.field_auditlog_http_request__create_uid
#: model:ir.model.fields,field_description:auditlog.field_auditlog_http_session__create_uid
#: model:ir.model.fields,field_description:auditlog.field_auditlog_log__create_uid
#: model:ir.model.fields,field_description:auditlog.field_auditlog_log_line__create_uid
#: model:ir.model.fields,field_description:auditlog.field_auditlog_log_line_view__create_uid
#: model:ir.model.fields,field_description:auditlog.field_auditlog_rule__create_uid
msgid "Created by"
msgstr "Skapad av"

#. module: auditlog
#: model:ir.model.fields,field_description:auditlog.field_auditlog_autovacuum__create_date
#: model:ir.model.fields,field_description:auditlog.field_auditlog_http_request__create_date
#: model:ir.model.fields,field_description:auditlog.field_auditlog_http_session__create_date
#: model:ir.model.fields,field_description:auditlog.field_auditlog_log__create_date
#: model:ir.model.fields,field_description:auditlog.field_auditlog_log_line__create_date
#: model:ir.model.fields,field_description:auditlog.field_auditlog_log_line_view__create_date
#: model:ir.model.fields,field_description:auditlog.field_auditlog_rule__create_date
#: model_terms:ir.ui.view,arch_db:auditlog.view_auditlog_http_request_search
#: model_terms:ir.ui.view,arch_db:auditlog.view_auditlog_http_session_search
msgid "Created on"
msgstr "Skapad den"

#. module: auditlog
#: model_terms:ir.ui.view,arch_db:auditlog.view_auditlog_line_search
#: model_terms:ir.ui.view,arch_db:auditlog.view_auditlog_log_search
msgid "Date"
msgstr "Datum"

#. module: auditlog
#: model:ir.model.fields,field_description:auditlog.field_auditlog_log_line__field_description
#: model:ir.model.fields,field_description:auditlog.field_auditlog_log_line_view__field_description
msgid "Description"
msgstr "Beskrivning"

#. module: auditlog
#: model:ir.model.fields,field_description:auditlog.field_auditlog_autovacuum__display_name
#: model:ir.model.fields,field_description:auditlog.field_auditlog_log__display_name
#: model:ir.model.fields,field_description:auditlog.field_auditlog_log_line__display_name
#: model:ir.model.fields,field_description:auditlog.field_auditlog_log_line_view__display_name
#: model:ir.model.fields,field_description:auditlog.field_auditlog_rule__display_name
msgid "Display Name"
msgstr "Visa namn"

#. module: auditlog
#: model:ir.model.fields.selection,name:auditlog.selection__auditlog_rule__state__draft
#: model_terms:ir.ui.view,arch_db:auditlog.view_auditlog_rule_search
msgid "Draft"
msgstr "Preliminär"

#. module: auditlog
#: model:ir.model.fields.selection,name:auditlog.selection__auditlog_log__log_type__fast
#: model:ir.model.fields.selection,name:auditlog.selection__auditlog_rule__log_type__fast
msgid "Fast log"
msgstr "Snabb logg"

#. module: auditlog
#: model:ir.model.fields,field_description:auditlog.field_auditlog_log_line__field_id
#: model:ir.model.fields,field_description:auditlog.field_auditlog_log_line_view__field_id
#: model_terms:ir.ui.view,arch_db:auditlog.view_auditlog_line_search
msgid "Field"
msgstr "Fält"

#. module: auditlog
#. odoo-python
#: code:addons/auditlog/models/rule.py:0
msgid "Field 'model_id' cannot be empty."
msgstr "Fältet \"model_id\" får inte vara tomt."

#. module: auditlog
#: model:ir.model.fields,field_description:auditlog.field_auditlog_rule__fields_to_exclude_ids
msgid "Fields to Exclude"
msgstr "Fält att utesluta"

#. module: auditlog
#: model:ir.model.fields,field_description:auditlog.field_auditlog_log__line_ids
#: model_terms:ir.ui.view,arch_db:auditlog.view_auditlog_log_form
msgid "Fields updated"
msgstr "Fält uppdaterade"

#. module: auditlog
#: model:ir.model.fields.selection,name:auditlog.selection__auditlog_log__log_type__full
#: model:ir.model.fields.selection,name:auditlog.selection__auditlog_rule__log_type__full
msgid "Full log"
msgstr "Full logg"

#. module: auditlog
#: model:ir.model.fields,help:auditlog.field_auditlog_rule__log_type
msgid ""
"Full log: make a diff between the data before and after the operation (log "
"more info like computed fields which were updated, but it is slower)\n"
"Fast log: only log the changes made through the create and write operations "
"(less information, but it is faster)"
msgstr ""
"Full log: gör en skillnad mellan data före och efter operationen (loggar mer "
"information, t.ex. beräknade fält som uppdaterades, men det går "
"långsammare)\n"
"Snabb loggning: loggar endast de ändringar som gjorts genom skapande- och "
"skrivoperationerna (mindre information, men det går snabbare)"

#. module: auditlog
#: model_terms:ir.ui.view,arch_db:auditlog.view_auditlog_http_request_search
#: model_terms:ir.ui.view,arch_db:auditlog.view_auditlog_http_session_search
#: model_terms:ir.ui.view,arch_db:auditlog.view_auditlog_line_search
#: model_terms:ir.ui.view,arch_db:auditlog.view_auditlog_log_search
#: model_terms:ir.ui.view,arch_db:auditlog.view_auditlog_rule_search
msgid "Group By..."
msgstr "Gruppera efter..."

#. module: auditlog
#: model_terms:ir.ui.view,arch_db:auditlog.view_auditlog_log_form
msgid "HTTP Context"
msgstr "HTTP-kontext"

#. module: auditlog
#: model:ir.model.fields,field_description:auditlog.field_auditlog_log__http_request_id
#: model:ir.model.fields,field_description:auditlog.field_auditlog_log_line_view__http_request_id
#: model_terms:ir.ui.view,arch_db:auditlog.view_auditlog_http_request_form
#: model_terms:ir.ui.view,arch_db:auditlog.view_auditlog_line_search
#: model_terms:ir.ui.view,arch_db:auditlog.view_auditlog_log_search
msgid "HTTP Request"
msgstr "HTTP-förfrågan"

#. module: auditlog
#: model:ir.actions.act_window,name:auditlog.action_auditlog_http_request_tree
#: model:ir.model.fields,field_description:auditlog.field_auditlog_http_session__http_request_ids
#: model:ir.ui.menu,name:auditlog.menu_action_auditlog_http_request_tree
#: model_terms:ir.ui.view,arch_db:auditlog.view_auditlog_http_request_search
#: model_terms:ir.ui.view,arch_db:auditlog.view_auditlog_http_session_form
msgid "HTTP Requests"
msgstr "HTTP-förfrågningar"

#. module: auditlog
#: model:ir.model.fields,field_description:auditlog.field_auditlog_autovacuum__id
#: model:ir.model.fields,field_description:auditlog.field_auditlog_http_request__id
#: model:ir.model.fields,field_description:auditlog.field_auditlog_http_session__id
#: model:ir.model.fields,field_description:auditlog.field_auditlog_log__id
#: model:ir.model.fields,field_description:auditlog.field_auditlog_log_line__id
#: model:ir.model.fields,field_description:auditlog.field_auditlog_log_line_view__id
#: model:ir.model.fields,field_description:auditlog.field_auditlog_rule__id
msgid "ID"
msgstr "ID"

#. module: auditlog
#: model:ir.model.fields,field_description:auditlog.field_auditlog_autovacuum__write_uid
#: model:ir.model.fields,field_description:auditlog.field_auditlog_http_request__write_uid
#: model:ir.model.fields,field_description:auditlog.field_auditlog_http_session__write_uid
#: model:ir.model.fields,field_description:auditlog.field_auditlog_log__write_uid
#: model:ir.model.fields,field_description:auditlog.field_auditlog_log_line__write_uid
#: model:ir.model.fields,field_description:auditlog.field_auditlog_log_line_view__write_uid
#: model:ir.model.fields,field_description:auditlog.field_auditlog_rule__write_uid
msgid "Last Updated by"
msgstr "Senast uppdaterad av"

#. module: auditlog
#: model:ir.model.fields,field_description:auditlog.field_auditlog_autovacuum__write_date
#: model:ir.model.fields,field_description:auditlog.field_auditlog_http_request__write_date
#: model:ir.model.fields,field_description:auditlog.field_auditlog_http_session__write_date
#: model:ir.model.fields,field_description:auditlog.field_auditlog_log__write_date
#: model:ir.model.fields,field_description:auditlog.field_auditlog_log_line__write_date
#: model:ir.model.fields,field_description:auditlog.field_auditlog_log_line_view__write_date
#: model:ir.model.fields,field_description:auditlog.field_auditlog_rule__write_date
msgid "Last Updated on"
msgstr "Senast uppdaterad"

#. module: auditlog
#: model:ir.model.fields,field_description:auditlog.field_auditlog_log_line__log_id
#: model:ir.model.fields,field_description:auditlog.field_auditlog_log_line_view__log_id
#: model_terms:ir.ui.view,arch_db:auditlog.view_auditlog_log_form
msgid "Log"
msgstr "Logg"

#. module: auditlog
#: model_terms:ir.ui.view,arch_db:auditlog.view_auditlog_log_form
msgid "Log - Field updated"
msgstr "Logg - Fältet uppdaterat"

#. module: auditlog
#: model:ir.model.fields,field_description:auditlog.field_auditlog_rule__log_create
msgid "Log Creates"
msgstr "Logg skapar"

#. module: auditlog
#: model:ir.model.fields,field_description:auditlog.field_auditlog_rule__log_unlink
msgid "Log Deletes"
msgstr "Loggen raderas"

#. module: auditlog
#: model:ir.actions.act_window,name:auditlog.action_auditlog_line
#: model:ir.ui.menu,name:auditlog.menu_auditlog_line
#: model_terms:ir.ui.view,arch_db:auditlog.view_auditlog_line_search
msgid "Log Lines"
msgstr "Logglinjer"

#. module: auditlog
#: model:ir.model.fields,field_description:auditlog.field_auditlog_rule__log_read
msgid "Log Reads"
msgstr "Loggläsningar"

#. module: auditlog
#: model:ir.model.fields,field_description:auditlog.field_auditlog_rule__log_write
msgid "Log Writes"
msgstr "Logg skrivningar"

#. module: auditlog
#: model:ir.actions.act_window,name:auditlog.action_auditlog_log_tree
#: model:ir.model.fields,field_description:auditlog.field_auditlog_http_request__log_ids
#: model:ir.ui.menu,name:auditlog.menu_audit_logs
#: model_terms:ir.ui.view,arch_db:auditlog.view_auditlog_http_request_form
#: model_terms:ir.ui.view,arch_db:auditlog.view_auditlog_log_search
msgid "Logs"
msgstr "Loggar"

#. module: auditlog
#: model:ir.model.fields,field_description:auditlog.field_auditlog_log__method
#: model:ir.model.fields,field_description:auditlog.field_auditlog_log_line_view__method
msgid "Method"
msgstr "Metod"

#. module: auditlog
#: model:ir.model.fields,field_description:auditlog.field_auditlog_log__model_id
#: model:ir.model.fields,field_description:auditlog.field_auditlog_log_line_view__model_id
#: model:ir.model.fields,field_description:auditlog.field_auditlog_rule__model_id
#: model_terms:ir.ui.view,arch_db:auditlog.view_auditlog_line_search
#: model_terms:ir.ui.view,arch_db:auditlog.view_auditlog_log_search
msgid "Model"
msgstr "Modell"

#. module: auditlog
#: model:ir.model.fields,field_description:auditlog.field_auditlog_log_line_view__model_model
msgid "Model Model"
msgstr "Modell Modell"

#. module: auditlog
#: model:ir.model.fields,field_description:auditlog.field_auditlog_log__model_name
#: model:ir.model.fields,field_description:auditlog.field_auditlog_log_line_view__model_name
#: model:ir.model.fields,field_description:auditlog.field_auditlog_rule__model_name
msgid "Model Name"
msgstr "Modellnamn"

#. module: auditlog
#: model:ir.model.fields,field_description:auditlog.field_auditlog_http_request__display_name
#: model:ir.model.fields,field_description:auditlog.field_auditlog_http_session__display_name
#: model:ir.model.fields,field_description:auditlog.field_auditlog_log_line_view__name
#: model:ir.model.fields,field_description:auditlog.field_auditlog_rule__name
msgid "Name"
msgstr "Namn"

#. module: auditlog
#: model:ir.model.fields,field_description:auditlog.field_auditlog_log_line__new_value
#: model:ir.model.fields,field_description:auditlog.field_auditlog_log_line_view__new_value
msgid "New Value"
msgstr "Nytt värde"

#. module: auditlog
#: model:ir.model.fields,field_description:auditlog.field_auditlog_log_line__new_value_text
#: model:ir.model.fields,field_description:auditlog.field_auditlog_log_line_view__new_value_text
msgid "New value Text"
msgstr "Nytt värde Text"

#. module: auditlog
#. odoo-python
#: code:addons/auditlog/models/log.py:0
msgid "No field defined to create line."
msgstr "Inget fält definierat för att skapa linje."

#. module: auditlog
#. odoo-python
#: code:addons/auditlog/models/rule.py:0
msgid "No model defined to create line."
msgstr "Ingen modell definierad för att skapa linjen."

#. module: auditlog
#. odoo-python
#: code:addons/auditlog/models/log.py:0
msgid "No model defined to create log."
msgstr "Ingen modell definierad för att skapa logg."

#. module: auditlog
#: model:ir.model.fields,field_description:auditlog.field_auditlog_log_line__old_value
#: model:ir.model.fields,field_description:auditlog.field_auditlog_log_line_view__old_value
msgid "Old Value"
msgstr "Gammalt värde"

#. module: auditlog
#: model:ir.model.fields,field_description:auditlog.field_auditlog_log_line__old_value_text
#: model:ir.model.fields,field_description:auditlog.field_auditlog_log_line_view__old_value_text
msgid "Old value Text"
msgstr "Gammalt värde Text"

#. module: auditlog
#: model:ir.model.fields,field_description:auditlog.field_auditlog_http_request__name
#: model_terms:ir.ui.view,arch_db:auditlog.view_auditlog_http_request_search
msgid "Path"
msgstr "Väg"

#. module: auditlog
#: model:ir.model.fields,field_description:auditlog.field_auditlog_log_line_view__res_id
msgid "Res"
msgstr "Res"

#. module: auditlog
#: model:ir.model.fields,field_description:auditlog.field_auditlog_log__res_id
#: model_terms:ir.ui.view,arch_db:auditlog.view_auditlog_line_search
#: model_terms:ir.ui.view,arch_db:auditlog.view_auditlog_log_search
msgid "Resource ID"
msgstr "Resurs-ID"

#. module: auditlog
#: model:ir.model.fields,field_description:auditlog.field_auditlog_log__name
msgid "Resource Name"
msgstr "Namn på resurs"

#. module: auditlog
#: model:ir.model.fields,field_description:auditlog.field_auditlog_http_request__root_url
#: model_terms:ir.ui.view,arch_db:auditlog.view_auditlog_http_request_search
msgid "Root URL"
msgstr "URL för rot"

#. module: auditlog
#: model_terms:ir.ui.view,arch_db:auditlog.view_auditlog_rule_form
msgid "Rule"
msgstr "Regel"

#. module: auditlog
#: model:ir.actions.act_window,name:auditlog.action_auditlog_rule_tree
#: model:ir.ui.menu,name:auditlog.menu_action_auditlog_rule_tree
#: model_terms:ir.ui.view,arch_db:auditlog.view_auditlog_rule_search
msgid "Rules"
msgstr "Regler"

#. module: auditlog
#: model:ir.model.fields,help:auditlog.field_auditlog_rule__model_id
msgid "Select model for which you want to generate log."
msgstr "Välj den modell som du vill generera loggen för."

#. module: auditlog
#: model:ir.model.fields,help:auditlog.field_auditlog_rule__capture_record
msgid "Select this if you want to keep track of Unlink Record"
msgstr "Välj detta om du vill hålla reda på Unlink Record"

#. module: auditlog
#: model:ir.model.fields,help:auditlog.field_auditlog_rule__log_create
msgid ""
"Select this if you want to keep track of creation on any record of the model "
"of this rule"
msgstr ""
"Välj detta om du vill hålla reda på skapandet på alla poster av modellen för "
"denna regel"

#. module: auditlog
#: model:ir.model.fields,help:auditlog.field_auditlog_rule__log_unlink
msgid ""
"Select this if you want to keep track of deletion on any record of the model "
"of this rule"
msgstr ""
"Välj detta om du vill hålla reda på borttagning på alla poster av modellen "
"för denna regel"

#. module: auditlog
#: model:ir.model.fields,help:auditlog.field_auditlog_rule__log_write
msgid ""
"Select this if you want to keep track of modification on any record of the "
"model of this rule"
msgstr ""
"Välj detta om du vill hålla reda på ändringar på alla poster av modellen för "
"denna regel"

#. module: auditlog
#: model:ir.model.fields,help:auditlog.field_auditlog_rule__log_read
msgid ""
"Select this if you want to keep track of read/open on any record of the "
"model of this rule"
msgstr ""
"Välj detta om du vill hålla reda på läst/öppen på alla poster av modellen "
"för denna regel"

#. module: auditlog
#: model:ir.model.fields,field_description:auditlog.field_auditlog_http_request__http_session_id
#: model:ir.model.fields,field_description:auditlog.field_auditlog_log__http_session_id
#: model:ir.model.fields,field_description:auditlog.field_auditlog_log_line_view__http_session_id
msgid "Session"
msgstr "Session"

#. module: auditlog
#: model:ir.model.fields,field_description:auditlog.field_auditlog_http_session__name
msgid "Session ID"
msgstr "Session ID"

#. module: auditlog
#: model:ir.model.fields,field_description:auditlog.field_auditlog_rule__state
#: model_terms:ir.ui.view,arch_db:auditlog.view_auditlog_rule_search
msgid "State"
msgstr "Status"

#. module: auditlog
#: model_terms:ir.ui.view,arch_db:auditlog.view_auditlog_rule_form
msgid "Subscribe"
msgstr "Prenumerera"

#. module: auditlog
#: model:ir.model.fields.selection,name:auditlog.selection__auditlog_rule__state__subscribed
#: model_terms:ir.ui.view,arch_db:auditlog.view_auditlog_rule_search
msgid "Subscribed"
msgstr "Prenumeration"

#. module: auditlog
#: model:ir.model.fields,field_description:auditlog.field_auditlog_log__model_model
#: model:ir.model.fields,field_description:auditlog.field_auditlog_rule__model_model
msgid "Technical Model Name"
msgstr "Teknisk modell Namn"

#. module: auditlog
#: model:ir.model.fields,field_description:auditlog.field_auditlog_log_line__field_name
#: model:ir.model.fields,field_description:auditlog.field_auditlog_log_line_view__field_name
msgid "Technical name"
msgstr "Tekniskt namn"

#. module: auditlog
#. odoo-python
#: code:addons/auditlog/models/log.py:0
msgid "The field 'field_id' cannot be empty."
msgstr "Fältet 'field_id' får inte vara tomt."

#. module: auditlog
#. odoo-python
#: code:addons/auditlog/models/log.py:0
msgid "The field 'model_id' cannot be empty."
msgstr "Fältet 'model_id' får inte vara tomt."

#. module: auditlog
#: model:ir.model.constraint,message:auditlog.constraint_auditlog_rule_model_uniq
msgid ""
"There is already a rule defined on this model\n"
"You cannot define another: please edit the existing one."
msgstr ""
"Det finns redan en regel definierad för den här modellen\n"
"Du kan inte definiera en ny: redigera den befintliga."

#. module: auditlog
#: model:ir.model.fields,field_description:auditlog.field_auditlog_log__log_type
#: model:ir.model.fields,field_description:auditlog.field_auditlog_log_line_view__log_type
#: model:ir.model.fields,field_description:auditlog.field_auditlog_rule__log_type
msgid "Type"
msgstr "Typ"

#. module: auditlog
#: model_terms:ir.ui.view,arch_db:auditlog.view_auditlog_rule_form
msgid "Unsubscribe"
msgstr "Avsluta prenumeration"

#. module: auditlog
#: model:ir.model.fields,field_description:auditlog.field_auditlog_http_request__user_id
#: model:ir.model.fields,field_description:auditlog.field_auditlog_http_session__user_id
#: model:ir.model.fields,field_description:auditlog.field_auditlog_log__user_id
#: model:ir.model.fields,field_description:auditlog.field_auditlog_log_line_view__user_id
#: model_terms:ir.ui.view,arch_db:auditlog.view_auditlog_http_request_search
#: model_terms:ir.ui.view,arch_db:auditlog.view_auditlog_http_session_search
#: model_terms:ir.ui.view,arch_db:auditlog.view_auditlog_line_search
#: model_terms:ir.ui.view,arch_db:auditlog.view_auditlog_log_search
msgid "User"
msgstr "Användare"

#. module: auditlog
#: model_terms:ir.ui.view,arch_db:auditlog.view_auditlog_http_request_search
#: model_terms:ir.ui.view,arch_db:auditlog.view_auditlog_http_session_form
#: model_terms:ir.ui.view,arch_db:auditlog.view_auditlog_line_search
#: model_terms:ir.ui.view,arch_db:auditlog.view_auditlog_log_search
msgid "User session"
msgstr "Användarsession"

#. module: auditlog
#: model:ir.actions.act_window,name:auditlog.action_auditlog_http_session_tree
#: model:ir.ui.menu,name:auditlog.menu_action_auditlog_http_session_tree
#: model_terms:ir.ui.view,arch_db:auditlog.view_auditlog_http_session_search
msgid "User sessions"
msgstr "Användarsessioner"

#. module: auditlog
#: model:ir.model.fields,field_description:auditlog.field_auditlog_rule__user_ids
msgid "Users"
msgstr "Användare"

#. module: auditlog
#: model:ir.model.fields,field_description:auditlog.field_auditlog_rule__users_to_exclude_ids
msgid "Users to Exclude"
msgstr "Användare att utesluta"

#. module: auditlog
#: model_terms:ir.ui.view,arch_db:auditlog.view_auditlog_log_form
msgid "Values"
msgstr "Värden"

#. module: auditlog
#. odoo-python
#: code:addons/auditlog/models/rule.py:0
msgid "View logs"
msgstr "Visa loggar"

#. module: auditlog
#: model:ir.model.fields,help:auditlog.field_auditlog_rule__user_ids
msgid "if no user is added then it will applicable for all users"
msgstr "om ingen användare läggs till kommer det att gälla för alla användare"

#~ msgid "Last Modified on"
#~ msgstr "Senast redigerad"
