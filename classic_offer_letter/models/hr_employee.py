# -*- coding: utf-8 -*-
from odoo import models, fields, api, _
from odoo.exceptions import ValidationError
import math
import logging
from dateutil.relativedelta import relativedelta 

_logger = logging.getLogger(__name__)

# Try to import num2words, if not available, provide a fallback (basic implementation)
try:
    from num2words import num2words
except ImportError:
    num2words = None
    _logger.warning("The 'num2words' library is not installed. Please install it for number to words conversion (e.g., pip install num2words). Falling back to basic conversion.")


class HrEmployee(models.Model):
    _inherit = 'hr.employee'

    offer_letter_employer_id = fields.Many2one('hr.employee', string='Employer', required=False)
    offer_letter_employer_designation = fields.Char(string='Employer Designation', required=False)
    
    @api.onchange('offer_letter_employer_id')
    def _onchange_employer_id(self):
        """Auto-fill designation from employer's function or set default"""
        if self.offer_letter_employer_id:
            self.offer_letter_employer_designation = self.offer_letter_employer_id.job_title or self.offer_letter_employer_id.job_id.name or ''
        else:
            self.offer_letter_employer_designation = False # Clear if employer is unselected
    
    # terms and conditions field
    terms_conditions_id = fields.Many2one(
        'terms.conditions', 
        string='Terms and Conditions', 
        help="The terms and conditions document associated with this contract."
    )      
    
    # memo numbers
    offer_memo_number = fields.Char(string="Offer Memo Number", readonly=True)
    appointment_memo_number = fields.Char(string="Appointment Memo No.", copy=False, readonly=True)
    
    joining_date = fields.Date(string="Joining Date", help="Official joining date for appointment letter")

    # --- New fields for Salary Components in Appointment Letter ---
    basic_salary_appointment = fields.Float(string="Basic Salary (Appointment)", compute='_compute_appointment_salary_details', store=False)
    house_rent_appointment = fields.Float(string="House Rent (Appointment)", compute='_compute_appointment_salary_details', store=False)
    medical_allowance_appointment = fields.Float(string="Medical Allowance (Appointment)", compute='_compute_appointment_salary_details', store=False)
    conveyance_allowance_appointment = fields.Float(string="Conveyance Allowance (Appointment)", compute='_compute_appointment_salary_details', store=False)
    food_allowance_appointment = fields.Float(string="Food Allowance (Appointment)", compute='_compute_appointment_salary_details', store=False)
    incentive_allowance_appointment = fields.Float(string="Incentive Allowance (Appointment)", compute='_compute_appointment_salary_details', store=False)
    total_salary_appointment = fields.Float(string="Total Salary (Appointment)", compute='_compute_appointment_salary_details', store=False)
    
    # fields for individual salary components
    basic_salary_appointment_words = fields.Char(string="Basic Salary (In Words) (Appointment)", compute='_compute_appointment_salary_details', store=False)
    house_rent_appointment_words = fields.Char(string="House Rent (In Words) (Appointment)", compute='_compute_appointment_salary_details', store=False)
    medical_allowance_appointment_words = fields.Char(string="Medical Allowance (In Words) (Appointment)", compute='_compute_appointment_salary_details', store=False)
    conveyance_allowance_appointment_words = fields.Char(string="Conveyance Allowance (In Words) (Appointment)", compute='_compute_appointment_salary_details', store=False)
    food_allowance_appointment_words = fields.Char(string="Food Allowance (In Words) (Appointment)", compute='_compute_appointment_salary_details', store=False)
    incentive_allowance_appointment_words = fields.Char(string="Incentive Allowance (In Words) (Appointment)", compute='_compute_appointment_salary_details', store=False)
    total_salary_appointment_words = fields.Char(string="Total Salary (In Words) (Appointment)", compute='_compute_appointment_salary_details', store=False)
    
    pay_grade_appointment = fields.Char(string="Pay Grade (Appointment)", compute='_compute_appointment_salary_details', store=False)
    pay_step_appointment = fields.Char(string="Pay Step (Appointment)", compute='_compute_appointment_salary_details', store=False)

    # Internal field to hold the found contract for report context (not stored in DB)
    _active_contract_id = fields.Many2one('hr.contract', compute='_compute_appointment_salary_details', store=False)


    # --- Helper functions (Copied/Adapted from previous module for consistency) ---

    def _num_to_words(self, num):
        """Converts a number to its English word representation (Indian numbering system)."""
        if num2words:
            try:
                amount_in_words = num2words(num, lang='en_IN', to='currency', currency='BDT')
                amount_in_words = amount_in_words.replace('BDT', 'Taka').replace('cents', 'Paisa').replace('point zero zero', '').strip()
                
                if 'Taka' in amount_in_words and not 'Paisa' in amount_in_words:
                    amount_in_words = amount_in_words.replace('Taka', 'Taka only')
                elif 'Paisa' in amount_in_words:
                    amount_in_words = amount_in_words.replace('Paisa', 'Paisa only')
                
                if amount_in_words.endswith('and only'):
                    amount_in_words = amount_in_words.replace('and only', 'only')

                return amount_in_words
            except Exception as e:
                _logger.error(f"Error converting amount to words with num2words: {e}")
                return self._amount_to_words_basic(num) + " only"
        else:
            return self._amount_to_words_basic(num) + " only"

    def _amount_to_words_basic(self, amount):
        """
        Basic fallback for converting a number to words if num2words is not installed.
        This is a very simple implementation and might not be accurate for all locales/currencies.
        For production, it's highly recommended to install 'num2words'.
        """
        if not amount:
            return "Zero"
        
        amount_int = int(math.floor(amount))
        words = []
        
        units = ["", "One", "Two", "Three", "Four", "Five", "Six", "Seven", "Eight", "Nine"]
        teens = ["Ten", "Eleven", "Twelve", "Thirteen", "Fourteen", "Fifteen", "Sixteen", "Seventeen", "Eighteen", "Nineteen"]
        tens = ["", "", "Twenty", "Thirty", "Forty", "Fifty", "Sixty", "Seventy", "Eighty", "Ninety"]
        
        def _convert_less_than_thousand(n):
            current_words = []
            if n == 0:
                return ""
            if n < 10:
                return units[n]
            if n < 20:
                return teens[n - 10]
            if n < 100:
                return tens[n // 10] + (" " + units[n % 10] if (n % 10 != 0) else "")
            return units[n // 100] + " Hundred" + (" " + _convert_less_than_thousand(n % 100) if (n % 100 != 0) else "")

        if amount_int == 0:
            words.append("Zero")
        
        # Indian numbering system scales
        scales = ["", "Thousand", "Lakh", "Crore", "Arab", "Kharab"]
        
        n = amount_int
        scale_index = 0

        while n > 0:
            if scale_index == 0: # First chunk (0-999)
                chunk = n % 1000
                n //= 1000
            else: # Subsequent chunks (0-99) for Lakh, Crore, etc.
                chunk = n % 100
                n //= 100
            
            if chunk > 0:
                current_words = _convert_less_than_thousand(chunk)
                if scale_index > 0:
                    current_words += " " + scales[scale_index]
                words.append(current_words)
            scale_index += 1

        result = " ".join(reversed(words)).strip()
        
        # Handle decimal part (Paisa)
        decimal_part = int(round((amount - amount_int) * 100))
        if decimal_part > 0:
            result += f" Taka and {self._convert_less_than_thousand(decimal_part)} Paisa"
        else:
            result += " Taka"
            
        return result

    def _evaluate_salary_rule_code_for_report(self, rule, contract, employee, total_wage, computed_amounts):
        """
        Safely evaluate salary rule code.
        This is a simplified version - for production use, implement proper security.
        """
        try:
            # Create a safe local dictionary for code evaluation
            localdict = {
                'contract': contract, 
                'employee': employee,
                'result': 0.0,
                'categories': computed_amounts, # amounts of previously computed rules
                'wage': total_wage,
                'rules': {}, # Placeholder for other rules if needed
                'payslip': None, # No payslip context here
                'worked_days': {}, # No worked days context here
                'inputs': {}, # No inputs context here
                'env': self.env, # Provide environment for database queries if needed by rule code
            }
            
            # Execute the rule code
            # Using compile and eval for safety, but still be cautious with user-defined code
            compiled_code = compile(rule.amount_python_compute, '<string>', 'exec')
            exec(compiled_code, {'__builtins__': {}}, localdict)
            return localdict.get('result', 0.0)
            
        except Exception as e:
            _logger.warning("Could not evaluate salary rule '%s' (%s) for contract %s: %s", rule.name, rule.code, contract.name, str(e))
            return 0.0

    def _get_contract_salary_breakdown(self, contract):
        """
        Calculate salary components based on the contract's salary structure/rules.
        If no structure is found or rules are not defined, it will return zero values.
        """
        salary_components = {
            'basic_salary': 0.0,
            'house_rent': 0.0,
            'medical_allowance': 0.0,
            'conveyance_allowance': 0.0,
            'food_allowance': 0.0,
            'incentive_allowance': 0.0,
            'total_salary': 0.0,
            'pay_grade': '',
            'pay_step': '',
        }

        if not contract or not contract.wage:
            return salary_components # Return empty if no contract or wage

        total_wage = contract.wage
        computed_amounts = {} # To store amounts of rules already computed

        # Define a mapping of desired fields to salary rule codes (ADJUST THESE CODES AS NEEDED)
        # These are common codes, verify them in your Odoo instance (Settings -> Technical -> Payroll -> Salary Rules)
        salary_rule_map = {
            'BASIC': 'basic_salary',
            'HRA': 'house_rent',
            'MEDALW': 'medical_allowance',
            'CONV': 'conveyance_allowance', # Common for Conveyance
            'FOOD': 'food_allowance',     # Common for Food Allowance
            'INCENTIVE': 'incentive_allowance', # Common for Incentive
            'GROSS': 'total_salary', # Gross salary rule
        }

        rules_found_and_processed = False
        if contract.structure_type_id:
            # Get the salary structure associated with the contract's structure type
            salary_structures = self.env['hr.payroll.structure'].search([
                ('type_id', '=', contract.structure_type_id.id),
                ('active', '=', True)
            ], limit=1) # Take the first active structure

            if salary_structures:
                salary_structure = salary_structures[0]
                rules = salary_structure.rule_ids.filtered(lambda r: r.active)

                if rules:
                    rules_found_and_processed = True
                    # First pass: Compute basic and percentage-based rules
                    for rule in rules.sorted(key='sequence'):
                        if rule.amount_select in ['fix', 'percentage']:
                            amount = 0.0
                            if rule.amount_select == 'fix':
                                amount = rule.amount_fix
                            elif rule.amount_select == 'percentage':
                                amount = (total_wage * rule.amount_percentage) / 100.0
                            computed_amounts[rule.code] = amount

                    # Second pass: Compute code-based rules, now that basic amounts are available
                    for rule in rules.sorted(key='sequence'):
                        if rule.amount_select == 'code':
                            amount = self._evaluate_salary_rule_code_for_report(rule, contract, contract.employee_id, total_wage, computed_amounts)
                            computed_amounts[rule.code] = amount
        
        # If rules_found_and_processed is False, salary_components will remain 0.0 as initialized.
        if rules_found_and_processed:
            # Map computed amounts from rules to the salary_components dictionary
            for code, field_name in salary_rule_map.items():
                salary_components[field_name] = computed_amounts.get(code, 0.0)
            
            # Ensure total_salary is correctly calculated if 'GROSS' rule is not explicit
            if not salary_components['total_salary']:
                # Sum of all earnings rules if 'GROSS' rule is not explicit
                earnings_sum = 0.0
                for rule_code, amount in computed_amounts.items():
                    rule_obj = self.env['hr.salary.rule'].search([('code', '=', rule_code)], limit=1)
                    # Assuming categories for earnings are 'BASIC' and 'ALW' (Allowance)
                    if rule_obj and rule_obj.category_id and rule_obj.category_id.code in ['BASIC', 'ALW']: 
                        earnings_sum += amount
                salary_components['total_salary'] = earnings_sum if earnings_sum else total_wage # Fallback to total wage if no earnings rules found

        # Get Pay Grade and Step from contract if available (assuming custom fields on hr.contract)
        # IMPORTANT: These fields (x_pay_grade, x_pay_step) must exist on hr.contract for this to work.
        # If they don't exist, they will remain empty strings.
        salary_components['pay_grade'] = contract.x_pay_grade if hasattr(contract, 'x_pay_grade') else ''
        salary_components['pay_step'] = contract.x_pay_step if hasattr(contract, 'x_pay_step') else ''

        return salary_components


    @api.depends('contract_id.wage', 'contract_id.state', 'contract_id.structure_type_id') # Removed x_pay_grade and x_pay_step from depends
    def _compute_appointment_salary_details(self):
        """
        Compute all appointment letter salary details from the employee's active contract.
        """
        for record in self:
            record.basic_salary_appointment = 0.0
            record.house_rent_appointment = 0.0
            record.medical_allowance_appointment = 0.0
            record.conveyance_allowance_appointment = 0.0
            record.food_allowance_appointment = 0.0
            record.incentive_allowance_appointment = 0.0
            record.total_salary_appointment = 0.0
            
            # FIXED: Initialize all words fields
            record.basic_salary_appointment_words = ""
            record.house_rent_appointment_words = ""
            record.medical_allowance_appointment_words = ""
            record.conveyance_allowance_appointment_words = ""
            record.food_allowance_appointment_words = ""
            record.incentive_allowance_appointment_words = ""
            record.total_salary_appointment_words = ""
            
            record.pay_grade_appointment = ""
            record.pay_step_appointment = ""
            record._active_contract_id = False

            if record.contract_id:
                # Find the active contract for the employee
                # Using contract_id directly as it's the current active contract in hr.employee
                active_contract = record.contract_id.filtered(lambda c: c.state == 'open' and (not c.date_end or c.date_end >= fields.Date.today()))
                
                if active_contract:
                    record._active_contract_id = active_contract[0] # Take the first active contract if multiple
                    salary_data = self._get_contract_salary_breakdown(record._active_contract_id)
                    
                    record.basic_salary_appointment = salary_data['basic_salary']
                    record.house_rent_appointment = salary_data['house_rent']
                    record.medical_allowance_appointment = salary_data['medical_allowance']
                    record.conveyance_allowance_appointment = salary_data['conveyance_allowance']
                    record.food_allowance_appointment = salary_data['food_allowance']
                    record.incentive_allowance_appointment = salary_data['incentive_allowance']
                    record.total_salary_appointment = salary_data['total_salary']
                    
                    # FIXED: Calculate words for each salary component
                    record.basic_salary_appointment_words = self._num_to_words(salary_data['basic_salary'])
                    record.house_rent_appointment_words = self._num_to_words(salary_data['house_rent'])
                    record.medical_allowance_appointment_words = self._num_to_words(salary_data['medical_allowance'])
                    record.conveyance_allowance_appointment_words = self._num_to_words(salary_data['conveyance_allowance'])
                    record.food_allowance_appointment_words = self._num_to_words(salary_data['food_allowance'])
                    record.incentive_allowance_appointment_words = self._num_to_words(salary_data['incentive_allowance'])
                    record.total_salary_appointment_words = self._num_to_words(salary_data['total_salary'])
                    
                    record.pay_grade_appointment = salary_data['pay_grade']
                    record.pay_step_appointment = salary_data['pay_step']
                else:
                    _logger.warning("No active contract found for employee %s. Appointment salary details will be zero.", record.name)
            else:
                _logger.warning("No contract linked to employee %s. Appointment salary details will be zero.", record.name)


    # --- Existing Memo Number Generation and Report Override ---

    def _generate_offer_memo_number(self):
        """Generate memo number for offer letter if not already set"""
        if not self.offer_memo_number:
            self.offer_memo_number = self.env['ir.sequence'].next_by_code('hr.employee.memo') 
            _logger.info(">>> Assigned offer memo number: %s to employee: %s", self.offer_memo_number, self.name)

    def action_generate_offer_memo_number(self):
        """Manual action to generate offer memo number"""
        for employee in self:
            if not employee.offer_memo_number:
                employee.offer_memo_number = self.env['ir.sequence'].next_by_code('hr.employee.memo')
                _logger.info(">>> Manually assigned offer memo number: %s to employee: %s", employee.offer_memo_number, employee.name)
        return True

    def _generate_appointment_memo_number(self):
        """Generate memo number for appointment letter if not already set"""
        if not self.appointment_memo_number:
            self.appointment_memo_number = self.env['ir.sequence'].next_by_code('hr.employee.appointment.memo')
            _logger.info(">>> Assigned appointment memo number: %s to employee: %s", self.appointment_memo_number, self.name)

    def action_generate_appointment_memo_number(self):
        """Manual action to generate appointment memo number"""
        for employee in self:
            if not employee.appointment_memo_number:
                employee.appointment_memo_number = self.env['ir.sequence'].next_by_code('hr.employee.appointment.memo')
                _logger.info(">>> Manually assigned appointment memo number: %s to employee: %s", employee.appointment_memo_number, employee.name)
        return True

class TermsConditions(models.Model):
    _name = 'terms.conditions'
    _description = 'Terms and Conditions for Letters'
    _rec_name = 'name'

    name = fields.Char(string='Name', required=True, help="Name/Title of the terms and conditions")
    content = fields.Html(string='Content', help="The actual terms and conditions content")

    def __str__(self):
        return self.name or ''

class IrActionsReport(models.Model):
    _inherit = 'ir.actions.report'

    def _render_qweb_pdf(self, report_ref, res_ids, data=None):
        """Override to generate memo numbers before rendering the offer/appointment letter reports"""
        if isinstance(report_ref, str):
            report = self._get_report(report_ref)
        else:
            report = report_ref

        # Check if this is our offer letter report
        if report and report.report_name == 'classic_offer_letter.report_employee_offer_letter_template':
            _logger.info(">>> Triggering offer memo generation for employees: %s", res_ids)
            employees = self.env['hr.employee'].browse(res_ids)
            for employee in employees:
                employee._generate_offer_memo_number()
                        
        # Check if this is our appointment letter report
        elif report and report.report_name in [
            'classic_offer_letter.report_employee_appointment_letter_template',
            'classic_offer_letter.report_employee_bengali_appointment_letter_template'
        ]:
            _logger.info(">>> Triggering appointment memo generation for employees: %s", res_ids)
            employees = self.env['hr.employee'].browse(res_ids)
            for employee in employees:
                employee._generate_appointment_memo_number()
        
        return super()._render_qweb_pdf(report_ref, res_ids, data)
    