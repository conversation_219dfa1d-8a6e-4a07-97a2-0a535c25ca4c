<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- Report Definition -->
        <report
            id="report_employee_offer_letter"
            model="hr.employee"
            string="Employee Offer Letter"
            report_type="qweb-pdf"
            name="classic_offer_letter.report_employee_offer_letter_template"
            file="classic_offer_letter.report_employee_offer_letter_template"
            print_report_name="(object.name + '_Offer_Letter')"
        />

        <!-- Separate Window Action for the Button -->
        <record id="action_report_employee_offer_letter" model="ir.actions.report">
            <field name="name">Employee Offer Letter</field>
            <field name="model">hr.employee</field>
            <field name="report_type">qweb-pdf</field>
            <field name="report_name">classic_offer_letter.report_employee_offer_letter_template</field>
            <field name="report_file">classic_offer_letter.report_employee_offer_letter_template</field>
            <field name="print_report_name">(object.name + '_Offer_Letter')</field>
            <field name="binding_model_id" ref="hr.model_hr_employee"/>
            <field name="binding_type">report</field>
        </record>

        <!-- QWeb Template for the Offer Letter -->
        <template id="report_employee_offer_letter_template">
            <t t-call="web.html_container">
                <t t-foreach="docs" t-as="o">
                    <t t-call="web.external_layout">
                        <div class="page" style="font-family: 'Inter', sans-serif; font-size: 11pt; line-height: 1.6; color: #333; padding: 0 20mm 20mm 20mm;">
                            <div class="oe_structure"/>
                            
                            <div style="display: flex; justify-content: space-between; font-size: 0.9em; margin-bottom: 30px;">
                                <t t-if="o.offer_memo_number">
                                    <span>Memo: <t t-esc="o.offer_memo_number"/></span>
                                </t>
                                <t t-if="not o.offer_memo_number">
                                    <span style="color: red;">[Memo number not set]</span>
                                </t>
                                <span>Date: <span t-esc="datetime.date.today().strftime('%d %b %Y')"/></span>
                            </div>

                            <div style="margin-bottom: 30px; font-size: 0.95em;">
                                <p style="font-weight: bold"> <span t-field="o.name"/></p>
                                
                                <!-- Private Address Section using direct fields -->
                                <!-- Street Address -->
                                <p t-if="o.private_street">
                                    Vill: <span t-field="o.private_street"/>
                                </p>
                                <!-- Street2 Address -->
                                <!-- <p t-if="o.private_street2">
                                    <span t-field="o.private_street2"/>
                                </p> -->
                                <!-- City, State, ZIP, Country  -->
                                <p t-if="o.private_city or o.private_state_id or o.private_zip">
                                    <t t-if="o.private_city">Dist: 
                                        <span t-field="o.private_city"/>
                                    </t>
                                    <t t-if="o.private_state_id">
                                        <t t-if="o.private_city">, </t>
                                        <span t-field="o.private_state_id.name"/>
                                    </t>
                                    <t t-if="o.private_zip">
                                        <t t-if="o.private_city or o.private_state_id"> </t>
                                        <span t-field="o.private_zip"/>,
                                    </t>
                                    <t t-if="o.private_country_id">
                                        <span t-field="o.private_country_id.name"/>
                                    </t>
                                </p>
                                <!-- -->
                                
                                
                                <!-- Fallback if no private address fields are set -->
                                <t t-if="not o.private_street and not o.private_city">
                                    <p style="color: #888; font-style: italic;">[Private address not set - please update employee record]</p>
                                    <p>_______________________________</p>
                                    <p>_______________________________</p>
                                    <p>_______________________________</p>
                                </t>
                                
                                <!-- Contact Information -->
                                <p t-if="o.private_phone">Mobile: <span t-field="o.private_phone"/></p>
                                <p t-if="o.private_email">E-mail: <span t-field="o.private_email"/></p>
                            </div>

                            <p style="font-weight: bold; margin-bottom: 20px;">
                                Subject: Employment Offer for the position of "<span t-field="o.job_id.name"/>".
                            </p>

                            <p>Dear <span t-field="o.name"/>,</p>

                            <p style="margin-top: 15px;">With reference to our discussions with you and your willingness to join our company, we are pleased to offer you employment as "<span t-field="o.job_id.name"/>", in Classic Chemical Industries Limited. Your employment shall commence on or before 15th April 2025.</p>

                            <p style="margin-top: 15px;">We kindly request that you submit a copy of your resignation acceptance letter from your current employer within three days of receiving this offer letter.</p>

                            <p style="margin-top: 15px;">Please bring the following papers on the date of joining:</p>
                            <t t-set="current_terms_content" t-value="report_data.get(o.id, {}).get('terms_content', '') if report_data else (o.terms_conditions_id.content if o.terms_conditions_id else '')"/>
                                <t t-if="current_terms_content">
                                    <t t-raw="current_terms_content"/>
                            </t>
                            
                            <p style="margin-top: 15px;">After getting acceptance, an appointment letter will be issued shortly.</p>

                            <p style="margin-top: 15px;">We believe our professional relationship will be beneficial, and we wish you the best in your new assignment.</p>

                            <p style="margin-top: 30px;">Yours Sincerely,</p>
                            <p>On behalf of Classic Chemical Industries Limited.</p>
                            <div style="margin-top: 40px; text-align: left;">
                                <div style="border-top: 1px solid #333; width: 200px; margin-bottom: 5px;"></div>
                                <t t-if="o.offer_letter_employer_id">
                                    <strong t-field="o.offer_letter_employer_id"/><br/>
                                    <span t-field="o.offer_letter_employer_designation"/>
                                </t>
                            </div>

                            <div style="margin-top: 30px; font-size: 0.85em;">
                                <p>Copy:</p>
                                <ul style="list-style-type: none; margin-left: 20px;">
                                    <li>01. Chairman, CCIL, Head Office, Gulshan, Dhaka</li>
                                    <li>02. Managing Director, CCIL, Head Office, Gulshan, Dhaka</li>
                                    <li>03. All Directors, CCIL, Head Office, Gulshan, Dhaka</li>
                                    <li>04. Md. Sharif Ullah, Project Director, CCIL, Head Office, Gulshan, Dhaka</li>
                                    <li>06. Office Copy</li>
                                </ul>
                            </div>
                        </div>
                    </t>
                </t>
            </t>
        </template>

    </data>
</odoo>
