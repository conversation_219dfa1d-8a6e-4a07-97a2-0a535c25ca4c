To configure this module, you need to:

- Go to Settings \> Technical \> Date ranges \> Date Range Types where
  you can create types of date ranges.

  ![](https://raw.githubusercontent.com/OCA/server-ux/18.0/date_range/static/description/date_range_type_create.png)

- Go to Settings \> Technical \> Date ranges \> Date Ranges where you
  can create date ranges.

  ![](https://raw.githubusercontent.com/OCA/server-ux/18.0/date_range/static/description/date_range_create.png)

  It's also possible to launch a wizard from the 'Generate Date Ranges'
  menu.

  ![](https://raw.githubusercontent.com/OCA/server-ux/18.0/date_range/static/description/date_range_wizard.png)

  The wizard is useful to generate recurring periods. Set an end date or
  enter the number of ranges to create.

  ![](https://raw.githubusercontent.com/OCA/server-ux/18.0/date_range/static/description/date_range_wizard_result.png)

- Your date ranges are now available in the search filter for any date
  or datetime fields

  Date range types are proposed as a filter operator

  ![](https://raw.githubusercontent.com/OCA/server-ux/18.0/date_range/static/description/date_range_type_as_filter.png)

  Once a type is selected, date ranges of this type are porposed as a
  filter value

  ![](https://raw.githubusercontent.com/OCA/server-ux/18.0/date_range/static/description/date_range_as_filter.png)

  And the dates specified into the date range are used to filter your
  result.

  ![](https://raw.githubusercontent.com/OCA/server-ux/18.0/date_range/static/description/date_range_as_filter_result.png)

- You can configure date range types with default values for the
  generation wizard on the Generation tab. In the same tab you can also
  configure date range types for auto-generation. New ranges for types
  configured for this are generated by a scheduled task that runs daily.

  ![](https://raw.githubusercontent.com/OCA/server-ux/18.0/date_range/static/description/date_range_type_autogeneration.png)
