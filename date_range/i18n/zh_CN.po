# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * date_range
#
# Translators:
# <AUTHOR> <EMAIL>, 2016
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 11.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-08-01 02:44+0000\n"
"PO-Revision-Date: 2024-08-27 12:06+0000\n"
"Last-Translator: xtanuiha <<EMAIL>>\n"
"Language-Team: Chinese (China) (https://www.transifex.com/oca/teams/23907/"
"zh_CN/)\n"
"Language: zh_CN\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=1; plural=0;\n"
"X-Generator: Weblate 5.6.2\n"

#. module: date_range
#. odoo-python
#: code:addons/date_range/models/date_range.py:0
msgid "%(name)s is not a valid range (%(date_start)s > %(date_end)s)"
msgstr "%(name)s不是有效的范围（%(date_start)s > %(date_end)s）"

#. module: date_range
#. odoo-python
#: code:addons/date_range/models/date_range.py:0
msgid "%(thisname)s overlaps %(dtname)s"
msgstr "%(thisname)s与%(dtname)s重叠"

#. module: date_range
#: model_terms:ir.ui.view,arch_db:date_range.date_range_generator_view_form
msgid ""
",\n"
"                                or generate"
msgstr ""
"，\n"
"                                或生成"

#. module: date_range
#: model:ir.model.constraint,message:date_range.constraint_date_range_date_range_uniq
msgid "A date range must be unique per company !"
msgstr "每个公司的日期范围必须是唯一的！"

#. module: date_range
#: model:ir.model.constraint,message:date_range.constraint_date_range_type_date_range_type_uniq
msgid "A date range type must be unique per company !"
msgstr "每个公司的日期范围类型必须是唯一的！"

#. module: date_range
#: model:ir.model.fields,field_description:date_range.field_date_range__active
#: model:ir.model.fields,field_description:date_range.field_date_range_type__active
msgid "Active"
msgstr "有效"

#. module: date_range
#: model:ir.model.fields,field_description:date_range.field_date_range_type__allow_overlap
msgid "Allow Overlap"
msgstr "允许重叠"

#. module: date_range
#: model_terms:ir.ui.view,arch_db:date_range.view_date_range_form_view
#: model_terms:ir.ui.view,arch_db:date_range.view_date_range_search
#: model_terms:ir.ui.view,arch_db:date_range.view_date_range_type_search
msgid "Archived"
msgstr "已归档"

#. module: date_range
#: model:ir.actions.server,name:date_range.ir_cron_autocreate_ir_actions_server
msgid "Auto-generate date ranges"
msgstr "自动生成日期范围"

#. module: date_range
#: model_terms:ir.ui.view,arch_db:date_range.view_date_range_type_form_view
msgid "Auto-generation settings"
msgstr "自动生成设置"

#. module: date_range
#: model:ir.model.fields,field_description:date_range.field_date_range_type__autogeneration_count
msgid "Autogeneration Count"
msgstr "自动生成数量"

#. module: date_range
#: model:ir.model.fields,field_description:date_range.field_date_range_type__autogeneration_date_start
msgid "Autogeneration Start Date"
msgstr "自动生成开始日期"

#. module: date_range
#: model:ir.model.fields,field_description:date_range.field_date_range_type__autogeneration_unit
msgid "Autogeneration Unit"
msgstr "自动生成单位"

#. module: date_range
#: model_terms:ir.ui.view,arch_db:date_range.date_range_generator_view_form
msgid "Cancel"
msgstr "取消"

#. module: date_range
#: model:ir.model.fields,field_description:date_range.field_date_range__company_id
#: model:ir.model.fields,field_description:date_range.field_date_range_generator__company_id
#: model:ir.model.fields,field_description:date_range.field_date_range_type__company_id
#: model_terms:ir.ui.view,arch_db:date_range.view_date_range_search
msgid "Company"
msgstr "公司"

#. module: date_range
#: model_terms:ir.ui.view,arch_db:date_range.view_date_range_type_form_view
msgid "Configuration"
msgstr "配置"

#. module: date_range
#: model_terms:ir.ui.view,arch_db:date_range.view_date_range_type_form_view
msgid "Create"
msgstr "创建"

#. module: date_range
#: model:ir.model.fields,field_description:date_range.field_date_range__create_uid
#: model:ir.model.fields,field_description:date_range.field_date_range_generator__create_uid
#: model:ir.model.fields,field_description:date_range.field_date_range_type__create_uid
msgid "Created by"
msgstr "创建者"

#. module: date_range
#: model:ir.model.fields,field_description:date_range.field_date_range__create_date
#: model:ir.model.fields,field_description:date_range.field_date_range_generator__create_date
#: model:ir.model.fields,field_description:date_range.field_date_range_type__create_date
msgid "Created on"
msgstr "创建时间"

#. module: date_range
#: model:ir.model,name:date_range.model_date_range
msgid "Date Range"
msgstr "日期范围"

#. module: date_range
#: model:ir.model,name:date_range.model_date_range_generator
msgid "Date Range Generator"
msgstr "日期范围生成器"

#. module: date_range
#: model:ir.model,name:date_range.model_date_range_type
#: model_terms:ir.ui.view,arch_db:date_range.view_date_range_type_form_view
msgid "Date Range Type"
msgstr "日期范围类型"

#. module: date_range
#: model:ir.actions.act_window,name:date_range.date_range_type_action
#: model:ir.ui.menu,name:date_range.menu_date_range_type_action
msgid "Date Range Types"
msgstr "日期范围类型"

#. module: date_range
#: model:ir.actions.act_window,name:date_range.date_range_action
#: model:ir.ui.menu,name:date_range.menu_date_range_action
msgid "Date Ranges"
msgstr "日期范围"

#. module: date_range
#: model:ir.model.fields,field_description:date_range.field_date_range_type__date_ranges_exist
msgid "Date Ranges Exist"
msgstr "日期范围已存在"

#. module: date_range
#: model:ir.ui.menu,name:date_range.menu_date_range
msgid "Date ranges"
msgstr "日期范围"

#. module: date_range
#: model_terms:ir.ui.view,arch_db:date_range.view_date_range_type_form_view
msgid "Defaults for generating date ranges"
msgstr "生成日期范围的默认设置"

#. module: date_range
#: model:ir.model.fields,field_description:date_range.field_date_range__display_name
#: model:ir.model.fields,field_description:date_range.field_date_range_generator__display_name
#: model:ir.model.fields,field_description:date_range.field_date_range_type__display_name
msgid "Display Name"
msgstr "显示名称"

#. module: date_range
#: model:ir.model.fields,field_description:date_range.field_date_range_generator__duration_count
#: model:ir.model.fields,field_description:date_range.field_date_range_type__duration_count
msgid "Duration"
msgstr "持续时间"

#. module: date_range
#: model_terms:ir.ui.view,arch_db:date_range.view_date_range_search
msgid "End Date"
msgstr "结束日期"

#. module: date_range
#: model:ir.model.fields,field_description:date_range.field_date_range__date_end
#: model:ir.model.fields,field_description:date_range.field_date_range_generator__date_end
msgid "End date"
msgstr "结束日期"

#. module: date_range
#: model:ir.model.fields,help:date_range.field_date_range_generator__name_expr
#: model:ir.model.fields,help:date_range.field_date_range_type__name_expr
msgid ""
"Evaluated expression. E.g. \"'FY%s' % date_start.strftime('%Y%m%d')\"\n"
"You can use the Date types 'date_end' and 'date_start', as well as the "
"'index' variable."
msgstr ""
"评估表达式。例如：\"'FY%s' % date_start.strftime('%Y%m%d')\"\n"
"你可以使用日期类型 'date_end' 和 'date_start'，以及 'index' 变量。"

#. module: date_range
#: model:ir.model.fields,field_description:date_range.field_date_range_search_mixin__date_range_search_id
msgid "Filter by period (technical field)"
msgstr "按周期过滤（技术字段）"

#. module: date_range
#: model_terms:ir.ui.view,arch_db:date_range.date_range_generator_view_form
msgid "Generate"
msgstr "生成"

#. module: date_range
#: model:ir.actions.act_window,name:date_range.date_range_generator_action
#: model:ir.ui.menu,name:date_range.menu_date_range_generator_action
#: model_terms:ir.ui.view,arch_db:date_range.date_range_generator_view_form
msgid "Generate Date Ranges"
msgstr "生成日期范围"

#. module: date_range
#: model_terms:ir.ui.view,arch_db:date_range.view_date_range_type_form_view
msgid "Generation"
msgstr "生成"

#. module: date_range
#: model:ir.model.fields,field_description:date_range.field_date_range__id
#: model:ir.model.fields,field_description:date_range.field_date_range_generator__id
#: model:ir.model.fields,field_description:date_range.field_date_range_type__id
msgid "ID"
msgstr "ID"

#. module: date_range
#: model:ir.model.fields,help:date_range.field_date_range_type__allow_overlap
msgid "If set, date ranges of same type must not overlap."
msgstr "如果设置，相同类型的日期范围不得重叠。"

#. module: date_range
#. odoo-python
#: code:addons/date_range/wizard/date_range_generator.py:0
msgid "Invalid name expression: %s"
msgstr "无效的名称表达式：%s"

#. module: date_range
#: model:ir.model.fields,field_description:date_range.field_date_range__write_uid
#: model:ir.model.fields,field_description:date_range.field_date_range_generator__write_uid
#: model:ir.model.fields,field_description:date_range.field_date_range_type__write_uid
msgid "Last Updated by"
msgstr "最后更新者"

#. module: date_range
#: model:ir.model.fields,field_description:date_range.field_date_range__write_date
#: model:ir.model.fields,field_description:date_range.field_date_range_generator__write_date
#: model:ir.model.fields,field_description:date_range.field_date_range_type__write_date
msgid "Last Updated on"
msgstr "最后更新时间"

#. module: date_range
#: model:ir.model,name:date_range.model_date_range_search_mixin
msgid "Mixin class to add a Many2one style period search field"
msgstr "混合类，用于添加Many2one风格的周期搜索字段"

#. module: date_range
#: model:ir.model.fields,field_description:date_range.field_date_range__name
#: model:ir.model.fields,field_description:date_range.field_date_range_type__name
msgid "Name"
msgstr "名称"

#. module: date_range
#. odoo-python
#: code:addons/date_range/wizard/date_range_generator.py:0
msgid "No ranges to generate with these settings"
msgstr "这些设置下没有范围可以生成"

#. module: date_range
#: model:ir.model.fields,field_description:date_range.field_date_range_generator__count
msgid "Number of ranges to generate"
msgstr "要生成的范围数"

#. module: date_range
#: model:ir.model.fields,help:date_range.field_date_range_type__autogeneration_date_start
msgid "Only applies when there are no date ranges of this type yet"
msgstr "仅在尚未有此类日期范围时适用"

#. module: date_range
#: model_terms:ir.ui.view,arch_db:date_range.date_range_generator_view_form
msgid "Parameters"
msgstr "参数"

#. module: date_range
#. odoo-python
#: code:addons/date_range/models/date_range_search_mixin.py:0
msgid "Period"
msgstr "周期"

#. module: date_range
#. odoo-python
#: code:addons/date_range/wizard/date_range_generator.py:0
msgid "Please enter an end date, or the number of ranges to generate."
msgstr "请输入结束日期，或要生成的范围数量。"

#. module: date_range
#. odoo-python
#: code:addons/date_range/wizard/date_range_generator.py:0
msgid "Please set a prefix or an expression to generate the range names."
msgstr "请设置一个前缀或表达式来生成范围名称。"

#. module: date_range
#: model:ir.model.fields,field_description:date_range.field_date_range_generator__range_name_preview
#: model:ir.model.fields,field_description:date_range.field_date_range_type__range_name_preview
msgid "Range Name Preview"
msgstr "范围名称预览"

#. module: date_range
#: model:ir.model.fields,field_description:date_range.field_date_range_generator__name_expr
#: model:ir.model.fields,field_description:date_range.field_date_range_type__name_expr
msgid "Range name expression"
msgstr "范围名称表达式"

#. module: date_range
#: model:ir.model.fields,field_description:date_range.field_date_range_generator__name_prefix
#: model:ir.model.fields,field_description:date_range.field_date_range_type__name_prefix
msgid "Range name prefix"
msgstr "范围名称前缀"

#. module: date_range
#: model:ir.model.fields,field_description:date_range.field_date_range_type__date_range_ids
msgid "Ranges"
msgstr "范围"

#. module: date_range
#: model_terms:ir.ui.view,arch_db:date_range.view_date_range_search
msgid "Start Date"
msgstr "开始日期"

#. module: date_range
#: model:ir.model.fields,field_description:date_range.field_date_range__date_start
#: model:ir.model.fields,field_description:date_range.field_date_range_generator__date_start
msgid "Start date"
msgstr "开始日期"

#. module: date_range
#. odoo-python
#: code:addons/date_range/wizard/date_range_generator.py:0
msgid ""
"The Company in the Date Range Generator and in Date Range Type must be the "
"same."
msgstr "日期范围生成器和日期范围类型中的公司必须相同。"

#. module: date_range
#: model:ir.model.fields,help:date_range.field_date_range_type__active
msgid ""
"The active field allows you to hide the date range type without removing it."
msgstr "活动字段允许您隐藏日期范围类型而不删除它。"

#. module: date_range
#: model:ir.model.fields,help:date_range.field_date_range__active
msgid "The active field allows you to hide the date range without removing it."
msgstr "活动字段允许您隐藏日期范围而不删除它。"

#. module: date_range
#: model:ir.model.fields,field_description:date_range.field_date_range__type_id
#: model:ir.model.fields,field_description:date_range.field_date_range_generator__type_id
msgid "Type"
msgstr "类型"

#. module: date_range
#: model:ir.model.fields,field_description:date_range.field_date_range_generator__unit_of_time
#: model:ir.model.fields,field_description:date_range.field_date_range_type__unit_of_time
msgid "Unit Of Time"
msgstr "单位时间"

#. module: date_range
#: model_terms:ir.ui.view,arch_db:date_range.date_range_generator_view_form
msgid "Until"
msgstr "直到"

#. module: date_range
#. odoo-python
#: code:addons/date_range/models/date_range_type.py:0
msgid ""
"You cannot change the company, as this Date Range Type is assigned to Date "
"Range '%s'."
msgstr "您不能更改公司，因为此日期范围类型已分配给日期范围'%s'。"

#. module: date_range
#: model:ir.model.fields.selection,name:date_range.selection__date_range_generator__unit_of_time__3
#: model:ir.model.fields.selection,name:date_range.selection__date_range_type__autogeneration_unit__3
#: model:ir.model.fields.selection,name:date_range.selection__date_range_type__unit_of_time__3
msgid "days"
msgstr "天"

#. module: date_range
#: model_terms:ir.ui.view,arch_db:date_range.date_range_generator_view_form
msgid "entries."
msgstr "条目。"

#. module: date_range
#: model_terms:ir.ui.view,arch_db:date_range.view_date_range_type_form_view
msgid "in advance"
msgstr "提前"

#. module: date_range
#: model:ir.model.fields.selection,name:date_range.selection__date_range_generator__unit_of_time__1
#: model:ir.model.fields.selection,name:date_range.selection__date_range_type__autogeneration_unit__1
#: model:ir.model.fields.selection,name:date_range.selection__date_range_type__unit_of_time__1
msgid "months"
msgstr "月"

#. module: date_range
#: model_terms:ir.ui.view,arch_db:date_range.date_range_generator_view_form
#: model_terms:ir.ui.view,arch_db:date_range.view_date_range_type_form_view
msgid "or enter a complex expression below"
msgstr "或在下方输入一个复杂表达式"

#. module: date_range
#: model:ir.model.fields.selection,name:date_range.selection__date_range_generator__unit_of_time__2
#: model:ir.model.fields.selection,name:date_range.selection__date_range_type__autogeneration_unit__2
#: model:ir.model.fields.selection,name:date_range.selection__date_range_type__unit_of_time__2
msgid "weeks"
msgstr "周"

#. module: date_range
#: model:ir.model.fields.selection,name:date_range.selection__date_range_generator__unit_of_time__0
#: model:ir.model.fields.selection,name:date_range.selection__date_range_type__autogeneration_unit__0
#: model:ir.model.fields.selection,name:date_range.selection__date_range_type__unit_of_time__0
msgid "years"
msgstr "年"

#~ msgid "&amp;nbsp;"
#~ msgstr "&amp;nbsp;"

#~ msgid "Type Name"
#~ msgstr "类型名称"

#~ msgid "Last Modified on"
#~ msgstr "最后修改时间"

#~ msgid "If sets date range of same type must not overlap."
#~ msgstr "如果设置相同类型的日期范围不得重叠。"

#~ msgid "Submit"
#~ msgstr "提交"

#, python-format
#~ msgid ""
#~ "You cannot change the company, as this Date Range Type is  assigned to "
#~ "Date Range (%s)."
#~ msgstr "您无法更改公司，因为此日期范围类型已分配给日期范围（%s）。"

#, python-format
#~ msgid "%s is not a valid range (%s > %s)"
#~ msgstr "%s 不是有效的日期范围 (%s > %s)"

#, python-format
#~ msgid "%s overlaps %s"
#~ msgstr "%s 重叠 %s"

#~ msgid "Date range"
#~ msgstr "日期范围"

#~ msgid "Date range type"
#~ msgstr "日期范围类型"

#, python-format
#~ msgid ""
#~ "The Company in the Date Range and in Date Range Type must be the same."
#~ msgstr "日期范围和日期范围类型中的公司必须相同。"

#~ msgid "Date Start"
#~ msgstr "开始日期"

#~ msgid "Genrate Date Ranges"
#~ msgstr "生成日期范围"
