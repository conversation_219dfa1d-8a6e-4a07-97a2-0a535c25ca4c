# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * date_range
#
# Translators:
# <AUTHOR> <EMAIL>, 2016
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 11.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-08-01 02:44+0000\n"
"PO-Revision-Date: 2024-05-23 12:35+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: French (https://www.transifex.com/oca/teams/23907/fr/)\n"
"Language: fr\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=n > 1;\n"
"X-Generator: Weblate 4.17\n"

#. module: date_range
#. odoo-python
#: code:addons/date_range/models/date_range.py:0
msgid "%(name)s is not a valid range (%(date_start)s > %(date_end)s)"
msgstr "%(name)s n’est pas une plage valide (%(date_start)s > %(date_end)s)"

#. module: date_range
#. odoo-python
#: code:addons/date_range/models/date_range.py:0
msgid "%(thisname)s overlaps %(dtname)s"
msgstr "%(thisname)s chevauche %(dtname)s"

#. module: date_range
#: model_terms:ir.ui.view,arch_db:date_range.date_range_generator_view_form
msgid ""
",\n"
"                                or generate"
msgstr ""
",\n"
"                                ou générer"

#. module: date_range
#: model:ir.model.constraint,message:date_range.constraint_date_range_date_range_uniq
msgid "A date range must be unique per company !"
msgstr "Une plage de dates doit être unique par société !"

#. module: date_range
#: model:ir.model.constraint,message:date_range.constraint_date_range_type_date_range_type_uniq
msgid "A date range type must be unique per company !"
msgstr "Un type de plage de dates doit être unique par société !"

#. module: date_range
#: model:ir.model.fields,field_description:date_range.field_date_range__active
#: model:ir.model.fields,field_description:date_range.field_date_range_type__active
msgid "Active"
msgstr "Actif"

#. module: date_range
#: model:ir.model.fields,field_description:date_range.field_date_range_type__allow_overlap
msgid "Allow Overlap"
msgstr "Autoriser le chevauchement"

#. module: date_range
#: model_terms:ir.ui.view,arch_db:date_range.view_date_range_form_view
#: model_terms:ir.ui.view,arch_db:date_range.view_date_range_search
#: model_terms:ir.ui.view,arch_db:date_range.view_date_range_type_search
msgid "Archived"
msgstr "Archivé"

#. module: date_range
#: model:ir.actions.server,name:date_range.ir_cron_autocreate_ir_actions_server
msgid "Auto-generate date ranges"
msgstr "Générer automatiquement des plages de dates"

#. module: date_range
#: model_terms:ir.ui.view,arch_db:date_range.view_date_range_type_form_view
msgid "Auto-generation settings"
msgstr "Paramètres de génération automatique"

#. module: date_range
#: model:ir.model.fields,field_description:date_range.field_date_range_type__autogeneration_count
msgid "Autogeneration Count"
msgstr "Nombre de plages autogénérées"

#. module: date_range
#: model:ir.model.fields,field_description:date_range.field_date_range_type__autogeneration_date_start
msgid "Autogeneration Start Date"
msgstr "Date de début de l'autogénération"

#. module: date_range
#: model:ir.model.fields,field_description:date_range.field_date_range_type__autogeneration_unit
msgid "Autogeneration Unit"
msgstr "Unité d'autogénération"

#. module: date_range
#: model_terms:ir.ui.view,arch_db:date_range.date_range_generator_view_form
msgid "Cancel"
msgstr "Annuler"

#. module: date_range
#: model:ir.model.fields,field_description:date_range.field_date_range__company_id
#: model:ir.model.fields,field_description:date_range.field_date_range_generator__company_id
#: model:ir.model.fields,field_description:date_range.field_date_range_type__company_id
#: model_terms:ir.ui.view,arch_db:date_range.view_date_range_search
msgid "Company"
msgstr "Société"

#. module: date_range
#: model_terms:ir.ui.view,arch_db:date_range.view_date_range_type_form_view
msgid "Configuration"
msgstr "Configuration"

#. module: date_range
#: model_terms:ir.ui.view,arch_db:date_range.view_date_range_type_form_view
msgid "Create"
msgstr "Créer"

#. module: date_range
#: model:ir.model.fields,field_description:date_range.field_date_range__create_uid
#: model:ir.model.fields,field_description:date_range.field_date_range_generator__create_uid
#: model:ir.model.fields,field_description:date_range.field_date_range_type__create_uid
msgid "Created by"
msgstr "Créé par"

#. module: date_range
#: model:ir.model.fields,field_description:date_range.field_date_range__create_date
#: model:ir.model.fields,field_description:date_range.field_date_range_generator__create_date
#: model:ir.model.fields,field_description:date_range.field_date_range_type__create_date
msgid "Created on"
msgstr "Créé le"

#. module: date_range
#: model:ir.model,name:date_range.model_date_range
msgid "Date Range"
msgstr "Plage de dates"

#. module: date_range
#: model:ir.model,name:date_range.model_date_range_generator
msgid "Date Range Generator"
msgstr "Générateur de plages de dates"

#. module: date_range
#: model:ir.model,name:date_range.model_date_range_type
#: model_terms:ir.ui.view,arch_db:date_range.view_date_range_type_form_view
msgid "Date Range Type"
msgstr "Type de plage de dates"

#. module: date_range
#: model:ir.actions.act_window,name:date_range.date_range_type_action
#: model:ir.ui.menu,name:date_range.menu_date_range_type_action
msgid "Date Range Types"
msgstr "Types de plages de dates"

#. module: date_range
#: model:ir.actions.act_window,name:date_range.date_range_action
#: model:ir.ui.menu,name:date_range.menu_date_range_action
msgid "Date Ranges"
msgstr "Plages de dates"

#. module: date_range
#: model:ir.model.fields,field_description:date_range.field_date_range_type__date_ranges_exist
msgid "Date Ranges Exist"
msgstr "Existence de plages de dates"

#. module: date_range
#: model:ir.ui.menu,name:date_range.menu_date_range
msgid "Date ranges"
msgstr "Plages de dates"

#. module: date_range
#: model_terms:ir.ui.view,arch_db:date_range.view_date_range_type_form_view
msgid "Defaults for generating date ranges"
msgstr "Valeurs par défaut pour la génération de plages de dates"

#. module: date_range
#: model:ir.model.fields,field_description:date_range.field_date_range__display_name
#: model:ir.model.fields,field_description:date_range.field_date_range_generator__display_name
#: model:ir.model.fields,field_description:date_range.field_date_range_type__display_name
msgid "Display Name"
msgstr "Nom affiché"

#. module: date_range
#: model:ir.model.fields,field_description:date_range.field_date_range_generator__duration_count
#: model:ir.model.fields,field_description:date_range.field_date_range_type__duration_count
msgid "Duration"
msgstr "Durée"

#. module: date_range
#: model_terms:ir.ui.view,arch_db:date_range.view_date_range_search
msgid "End Date"
msgstr "Date de fin"

#. module: date_range
#: model:ir.model.fields,field_description:date_range.field_date_range__date_end
#: model:ir.model.fields,field_description:date_range.field_date_range_generator__date_end
msgid "End date"
msgstr "Date de fin"

#. module: date_range
#: model:ir.model.fields,help:date_range.field_date_range_generator__name_expr
#: model:ir.model.fields,help:date_range.field_date_range_type__name_expr
msgid ""
"Evaluated expression. E.g. \"'FY%s' % date_start.strftime('%Y%m%d')\"\n"
"You can use the Date types 'date_end' and 'date_start', as well as the "
"'index' variable."
msgstr ""
"Expression évaluée. Par exemple : \"'FY%s' % date_start."
"strftime('%Y%m%d')\"\n"
"Vous pouvez utiliser les types de date 'date_end' et 'date_start', ainsi que "
"la variable 'index'."

#. module: date_range
#: model:ir.model.fields,field_description:date_range.field_date_range_search_mixin__date_range_search_id
msgid "Filter by period (technical field)"
msgstr "Filtrer par période (champ technique)"

#. module: date_range
#: model_terms:ir.ui.view,arch_db:date_range.date_range_generator_view_form
msgid "Generate"
msgstr "Générer"

#. module: date_range
#: model:ir.actions.act_window,name:date_range.date_range_generator_action
#: model:ir.ui.menu,name:date_range.menu_date_range_generator_action
#: model_terms:ir.ui.view,arch_db:date_range.date_range_generator_view_form
msgid "Generate Date Ranges"
msgstr "Générer les plages de dates"

#. module: date_range
#: model_terms:ir.ui.view,arch_db:date_range.view_date_range_type_form_view
msgid "Generation"
msgstr "Génération"

#. module: date_range
#: model:ir.model.fields,field_description:date_range.field_date_range__id
#: model:ir.model.fields,field_description:date_range.field_date_range_generator__id
#: model:ir.model.fields,field_description:date_range.field_date_range_type__id
msgid "ID"
msgstr "ID"

#. module: date_range
#: model:ir.model.fields,help:date_range.field_date_range_type__allow_overlap
msgid "If set, date ranges of same type must not overlap."
msgstr ""
"Si coché, les plages de dates du même type ne doivent pas se chevaucher."

#. module: date_range
#. odoo-python
#: code:addons/date_range/wizard/date_range_generator.py:0
msgid "Invalid name expression: %s"
msgstr "Expression non valide pour le nom : %s"

#. module: date_range
#: model:ir.model.fields,field_description:date_range.field_date_range__write_uid
#: model:ir.model.fields,field_description:date_range.field_date_range_generator__write_uid
#: model:ir.model.fields,field_description:date_range.field_date_range_type__write_uid
msgid "Last Updated by"
msgstr "Dernière mise à jour par"

#. module: date_range
#: model:ir.model.fields,field_description:date_range.field_date_range__write_date
#: model:ir.model.fields,field_description:date_range.field_date_range_generator__write_date
#: model:ir.model.fields,field_description:date_range.field_date_range_type__write_date
msgid "Last Updated on"
msgstr "Dernière mise à jour le"

#. module: date_range
#: model:ir.model,name:date_range.model_date_range_search_mixin
msgid "Mixin class to add a Many2one style period search field"
msgstr ""
"Classe mixin pour ajouter un champ de recherche par période de type Many2one"

#. module: date_range
#: model:ir.model.fields,field_description:date_range.field_date_range__name
#: model:ir.model.fields,field_description:date_range.field_date_range_type__name
msgid "Name"
msgstr "Nom"

#. module: date_range
#. odoo-python
#: code:addons/date_range/wizard/date_range_generator.py:0
msgid "No ranges to generate with these settings"
msgstr "Aucune plage à générer avec ces paramètres"

#. module: date_range
#: model:ir.model.fields,field_description:date_range.field_date_range_generator__count
msgid "Number of ranges to generate"
msgstr "Nombre de plages à générer"

#. module: date_range
#: model:ir.model.fields,help:date_range.field_date_range_type__autogeneration_date_start
msgid "Only applies when there are no date ranges of this type yet"
msgstr "Ne s'applique que s'il n'y a pas encore de plages de dates de ce type"

#. module: date_range
#: model_terms:ir.ui.view,arch_db:date_range.date_range_generator_view_form
msgid "Parameters"
msgstr "Paramètres"

#. module: date_range
#. odoo-python
#: code:addons/date_range/models/date_range_search_mixin.py:0
msgid "Period"
msgstr "Période"

#. module: date_range
#. odoo-python
#: code:addons/date_range/wizard/date_range_generator.py:0
msgid "Please enter an end date, or the number of ranges to generate."
msgstr "Veuillez saisir une date de fin ou le nombre de plages à générer."

#. module: date_range
#. odoo-python
#: code:addons/date_range/wizard/date_range_generator.py:0
msgid "Please set a prefix or an expression to generate the range names."
msgstr ""
"Veuillez définir un préfixe ou une expression pour générer les noms des "
"plages."

#. module: date_range
#: model:ir.model.fields,field_description:date_range.field_date_range_generator__range_name_preview
#: model:ir.model.fields,field_description:date_range.field_date_range_type__range_name_preview
msgid "Range Name Preview"
msgstr "Aperçu du nom des plages"

#. module: date_range
#: model:ir.model.fields,field_description:date_range.field_date_range_generator__name_expr
#: model:ir.model.fields,field_description:date_range.field_date_range_type__name_expr
msgid "Range name expression"
msgstr "Expression du nom des plages"

#. module: date_range
#: model:ir.model.fields,field_description:date_range.field_date_range_generator__name_prefix
#: model:ir.model.fields,field_description:date_range.field_date_range_type__name_prefix
msgid "Range name prefix"
msgstr "Préfixe du nom des plages"

#. module: date_range
#: model:ir.model.fields,field_description:date_range.field_date_range_type__date_range_ids
msgid "Ranges"
msgstr "Plage"

#. module: date_range
#: model_terms:ir.ui.view,arch_db:date_range.view_date_range_search
msgid "Start Date"
msgstr "Date de début"

#. module: date_range
#: model:ir.model.fields,field_description:date_range.field_date_range__date_start
#: model:ir.model.fields,field_description:date_range.field_date_range_generator__date_start
msgid "Start date"
msgstr "Date de début"

#. module: date_range
#. odoo-python
#: code:addons/date_range/wizard/date_range_generator.py:0
msgid ""
"The Company in the Date Range Generator and in Date Range Type must be the "
"same."
msgstr ""
"La société du générateur de plage de dates et du type de plage de dates doit "
"être la même."

#. module: date_range
#: model:ir.model.fields,help:date_range.field_date_range_type__active
msgid ""
"The active field allows you to hide the date range type without removing it."
msgstr ""
"Le champ actif permet de masquer le type de plage de dates sans le supprimer."

#. module: date_range
#: model:ir.model.fields,help:date_range.field_date_range__active
msgid "The active field allows you to hide the date range without removing it."
msgstr "Le champ actif permet de masquer la plage de dates sans la supprimer."

#. module: date_range
#: model:ir.model.fields,field_description:date_range.field_date_range__type_id
#: model:ir.model.fields,field_description:date_range.field_date_range_generator__type_id
msgid "Type"
msgstr "Type"

#. module: date_range
#: model:ir.model.fields,field_description:date_range.field_date_range_generator__unit_of_time
#: model:ir.model.fields,field_description:date_range.field_date_range_type__unit_of_time
msgid "Unit Of Time"
msgstr "Unité de temps"

#. module: date_range
#: model_terms:ir.ui.view,arch_db:date_range.date_range_generator_view_form
msgid "Until"
msgstr "Jusqu'à"

#. module: date_range
#. odoo-python
#: code:addons/date_range/models/date_range_type.py:0
msgid ""
"You cannot change the company, as this Date Range Type is assigned to Date "
"Range '%s'."
msgstr ""
"Vous ne pouvez changer la société, car ce type de plage de date est assigné "
"à la plage de date « %s »."

#. module: date_range
#: model:ir.model.fields.selection,name:date_range.selection__date_range_generator__unit_of_time__3
#: model:ir.model.fields.selection,name:date_range.selection__date_range_type__autogeneration_unit__3
#: model:ir.model.fields.selection,name:date_range.selection__date_range_type__unit_of_time__3
msgid "days"
msgstr "jours"

#. module: date_range
#: model_terms:ir.ui.view,arch_db:date_range.date_range_generator_view_form
msgid "entries."
msgstr "entrées."

#. module: date_range
#: model_terms:ir.ui.view,arch_db:date_range.view_date_range_type_form_view
msgid "in advance"
msgstr "à l'avance"

#. module: date_range
#: model:ir.model.fields.selection,name:date_range.selection__date_range_generator__unit_of_time__1
#: model:ir.model.fields.selection,name:date_range.selection__date_range_type__autogeneration_unit__1
#: model:ir.model.fields.selection,name:date_range.selection__date_range_type__unit_of_time__1
msgid "months"
msgstr "mois"

#. module: date_range
#: model_terms:ir.ui.view,arch_db:date_range.date_range_generator_view_form
#: model_terms:ir.ui.view,arch_db:date_range.view_date_range_type_form_view
msgid "or enter a complex expression below"
msgstr "ou saisissez une expression complexe ci-dessous"

#. module: date_range
#: model:ir.model.fields.selection,name:date_range.selection__date_range_generator__unit_of_time__2
#: model:ir.model.fields.selection,name:date_range.selection__date_range_type__autogeneration_unit__2
#: model:ir.model.fields.selection,name:date_range.selection__date_range_type__unit_of_time__2
msgid "weeks"
msgstr "semaines"

#. module: date_range
#: model:ir.model.fields.selection,name:date_range.selection__date_range_generator__unit_of_time__0
#: model:ir.model.fields.selection,name:date_range.selection__date_range_type__autogeneration_unit__0
#: model:ir.model.fields.selection,name:date_range.selection__date_range_type__unit_of_time__0
msgid "years"
msgstr "années"

#~ msgid "&amp;nbsp;"
#~ msgstr "&amp;nbsp;"

#~ msgid "Type Name"
#~ msgstr "Nom du type"

#~ msgid "Last Modified on"
#~ msgstr "Dernière modification le"

#~ msgid "If sets date range of same type must not overlap."
#~ msgstr ""
#~ "Si coché, les plages de dates du même type ne doivent pas se chevaucher."

#~ msgid "Submit"
#~ msgstr "Générer"

#, python-format
#~ msgid ""
#~ "You cannot change the company, as this Date Range Type is  assigned to "
#~ "Date Range (%s)."
#~ msgstr ""
#~ "Vous ne pouvez pas modifier la société, car ce type de plage de dates est "
#~ "affecté à la plage de dates '%s'."
