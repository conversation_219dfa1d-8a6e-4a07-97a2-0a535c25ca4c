<?xml version="1.0" encoding="utf-8" ?>
<odoo>
    <record id="view_stock_move_operations" model="ir.ui.view">
        <field name="name">stock.move.operations.form</field>
        <field name="model">stock.move</field>
        <field name="inherit_id" ref="stock.view_stock_move_operations" />
        <field name="arch" type="xml">
            <field name="move_line_ids" position="after">
                <newline />
                <field name="purchase_request_allocation_ids" />
            </field>
        </field>
    </record>
    <record id="purchase_request_allocation" model="ir.ui.view">
        <field name="name">purchase.request.allocation</field>
        <field name="model">purchase.request.allocation</field>
        <field name="arch" type="xml">
            <list name="Allocations">
                <field name="id" />
                <field name="purchase_request_line_id" />
                <field name="stock_move_id" />
                <field name="purchase_line_id" />
                <field name="product_id" />
                <field name="requested_product_uom_qty" />
                <field name="allocated_product_qty" />
                <field name="open_product_qty" />
            </list>
        </field>
    </record>
    <record id="view_move_form" model="ir.ui.view">
        <field name="name">stock.move.form</field>
        <field name="model">stock.move</field>
        <field name="inherit_id" ref="stock.view_move_form" />
        <field name="arch" type="xml">
            <group name="linked_group" position="after">
                <group
                    name="allocations"
                    string="Purchase Request Allocations"
                    colspan="2"
                >
                    <field name="purchase_request_allocation_ids" />
                </group>
            </group>
        </field>
    </record>
</odoo>
