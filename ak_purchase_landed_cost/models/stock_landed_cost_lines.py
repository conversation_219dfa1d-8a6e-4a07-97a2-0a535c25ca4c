# -*- coding: utf-8 -*-
# Part of Odoo, Aktiv Software
# See LICENSE file for full copyright & licensing details.

from odoo import models, fields, api


class StockLandedCostLine(models.Model):
    """
        Extends the stock landed cost lines to introduce
        a new split method 'By Product'.
    """
    _inherit = 'stock.landed.cost.lines'

    split_method = fields.Selection(
        selection_add=[('by_product', 'By Product')],
        ondelete={'by_product': 'cascade'},
        string='Split Method',
        required=True,
        help="Equal : Cost will be equally divided.\n" "By Quantity : Cost\
            will be divided according to product's \
            quantity.\n" "By Current cost : Cost will be divided according to \
            product's current cost.\n" "By Weight : Cost will be divided \
            depending on its weight.\n" "By Volume : Cost will be divided \
            depending on its volume. \n" "By Product : Cost will be divided \
            according product.")

    by_product_id = fields.Many2one('product.product', string="BY Product")

    @api.onchange('split_method')
    def onchange_split_method(self):
        """
               Dynamically updates the domain of the 'by_product_id'
               field based on the selected split method.
        """
        if self.split_method == 'by_product':
            picking_ids = self.cost_id.mapped('picking_ids').ids
            move_ids = self.env['stock.picking'].search([
                ('id', 'in', picking_ids)]).mapped('move_ids_without_package')
            product_ids = move_ids.mapped('product_id').ids

            return {'domain': {'by_product_id': [('id', 'in', product_ids)]}}
