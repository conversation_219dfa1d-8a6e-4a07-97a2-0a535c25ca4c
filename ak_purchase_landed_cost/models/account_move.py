# -*- coding: utf-8 -*-
# Part of Odoo, Aktiv Software
# See LICENSE file for full copyright & licensing details.

from odoo import models


class AccountMove(models.Model):
    """
       This class extends the 'account.move' model to customize the creation
       of landed costs when the  method is triggered.
    """
    _inherit = 'account.move'

    def button_create_landed_costs(self):
        """
        Override of the standard method to customize
        the creation of landed costs.

        :return: Result of the superclass method.
        :rtype: dict
        """
        res = super().button_create_landed_costs()

        purchase_id = (
            self.env['purchase.order'].search(
                [('name', '=', self.invoice_origin)]))
        stock_picking = purchase_id.picking_ids.ids \
            if purchase_id and purchase_id.picking_ids else []

        if res.get('res_id'):
            landed_cost_id = (
                self.env['stock.landed.cost'].browse(res['res_id']))

            landed_cost_id.write({
                'picking_ids': [(6, 0, stock_picking)],
                'cost_lines': [(1, c_l.id,
                               {'account_id': b_l.account_id})
                               for c_l, b_l in zip(
                    landed_cost_id.cost_lines,
                    landed_cost_id.vendor_bill_id.line_ids.filtered
                    (lambda line: line.is_landed_costs_line))
                               ],
            })
            res.update(res_id=landed_cost_id.id)
        return res
