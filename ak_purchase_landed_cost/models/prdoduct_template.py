# -*- coding: utf-8 -*-
# Part of Odoo, Aktiv Software
# See LICENSE file for full copyright & licensing details.

from odoo import models, fields


class ProductTemplate(models.Model):
    """
     Inherited:product template
    """
    _inherit = 'product.template'

    split_method_landed_cost = fields.Selection(selection_add=[
        ('by_product', 'By Product')],
        ondelete={'by_product': 'cascade'},
        string="Default Split Method",
        help="Default Split Method when used for Landed Cost")
