# -*- coding: utf-8 -*-
# Part of Odoo, Aktiv Software
# See LICENSE file for full copyright & licensing details.

from odoo import models, api


class ProductProduct(models.Model):
    """
        Inherited product.product to extend functionality
        related to product products.
    """
    _inherit = 'product.product'

    @api.model
    def _search(self, domain, offset=0, limit=None, order=None):
        """
           Override search to filter results based on the 'cost_id' context.
        """
        # TDE FIXME: strange
        if self._context.get('cost_id'):
            domain = domain.copy()
            landed_cost_id = (
                self.env["stock.landed.cost"].browse(self._context["cost_id"]))
            product_ids = (
                landed_cost_id.picking_ids.move_ids_without_package.product_id.ids
            )

            domain.append(('id', 'in', product_ids))
        return super()._search(domain, offset, limit, order)
